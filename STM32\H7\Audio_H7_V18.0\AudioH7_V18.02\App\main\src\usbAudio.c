#include "usbAudio.h"
#include "audio.h"
#include "appEntry.h"
#include "usb_device.h"
#include "usbd_audio.h"
#include "usbd_audio_if.h"
#include "filterCoeffs.h"
#include "H5.h"

extern int16_t audio_in[AUDIO_BUFFER_SIZE];
extern int16_t usb_audio_out[MIC_SAMPLES_PER_PACKET];

extern uint8_t audioRunning;
extern uint8_t audioDataReady;
extern uint8_t audioProcessing;
extern USBD_HandleTypeDef hUsbDeviceHS;
extern float outputVolume;
      // Final buffer sent to USB
bool _running = false;
uint8_t _zerocounter = 0;

HAL_StatusTypeDef usbAudioStart(void)
{
    HAL_StatusTypeDef status;
    const AudioSource_t *audio = getAudioSource();

    SCB_InvalidateDCache_by_Addr((uint32_t*)(((uint32_t)audio_in) & ~(uint32_t)0x1F), (MIC_SAMPLES_PER_PACKET * 2) + 32);
    __DSB();

    status = HAL_I2S_Receive_DMA(audio->InI2S, (uint16_t *)audio_in, (MIC_SAMPLES_PER_PACKET * 2));

    if (status == HAL_OK)
    {
        _running = true;
        printf("DMA Started\n");
    }
    else
    {
        Send2ParamEvent(EV_H5_ERROR, H5, ADC_ERROR_AUDIO_START_FAILED, ADC_ERROR_SUBCODE_I2S_RECEIVE);
        printf("DMA Failed\n");
    }
    return status;
}

HAL_StatusTypeDef usbAudioStop(void)
{
    printf("audioStop\n");
    HAL_StatusTypeDef status;
    AudioSource_t *audio = getAudioSource();

    if ((status = HAL_I2S_DMAStop(audio->InI2S)) == HAL_OK)
    {
        _running = false;
    }

    return status;
}

HAL_StatusTypeDef usbAudioPause(void)
{
    printf("audioPause\n");
    HAL_StatusTypeDef status;
    AudioSource_t *audio = getAudioSource();
    if ((status = HAL_I2S_DMAPause(audio->InI2S)) == HAL_OK)
    {
        _running = false;
    }
    return status;
}

void usbAudioSetVolume(int16_t volume)
{
    UNUSED(volume);
    // TODO: Implement volume control
}

HAL_StatusTypeDef usbAudioResume(void)
{
    printf("audioResume\n");
    HAL_StatusTypeDef status;
    const AudioSource_t *audio = getAudioSource();
    if ((status = HAL_I2S_DMAResume(audio->InI2S)) == HAL_OK)
    {
        _running = true;
    }
    return status;
}

HAL_StatusTypeDef usbAudioMute(bool mute)
{
    printf("audioMute\n");
    HAL_StatusTypeDef status = HAL_OK;
    static float prevVol;

    if (mute)
    {
        prevVol = outputVolume;
        outputVolume = 0;
    }
    else
    {
        outputVolume = prevVol;
    }
    return status;
}

