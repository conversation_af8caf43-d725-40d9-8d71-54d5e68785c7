{
    "version": 4,
    "configurations": [
        {
            /** 
             * ms-vscode.cmake-tools plugin should be installed.
             * 
             * It provides data for C/C++ plugin,
             * such as include paths, browse paths, defines, etc.
             */
            "name": "STM32",
            "configurationProvider": "ms-vscode.cmake-tools",
            "intelliSenseMode": "${default}"
        }
    ]
}