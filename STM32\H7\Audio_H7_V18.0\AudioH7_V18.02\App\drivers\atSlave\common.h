#ifndef __COMMON_H__
#define __COMMON_H__

#include <stdint.h>
#include <string.h>
#include "stm32h7xx_hal.h"

#define CMD_BUFFER_SIZE 28
#define EVT_BUFFER_SIZE 28
#define MAX_PARAMS_PER_EVENT 3
#define MAX_PARAM_LENGTH 20
#define MAX_DEVICES 3

typedef struct
{
    const char *event; // Command string (e.g., "AT+NAME")  
    uint8_t index;       // Unique identifier for the command
} event_pattern_t;

typedef enum {
    PARAM_TYPE_UINT8,
    PARAM_TYPE_FLOAT,
    PARAM_TYPE_CHAR,
    PARAM_TYPE_SIMPLE
} ParamType;

typedef struct {
    char command[EVT_BUFFER_SIZE];          // Event name
    char param[MAX_PARAMS_PER_EVENT][MAX_PARAM_LENGTH]; // Parameters as strings
    int num_params;                        // Number of parameters
    uint8_t device;
} command_t;

typedef struct 
{
    uint8_t device;
    uint8_t index;
    uint16_t param[MAX_PARAMS_PER_EVENT];
    char *c_param;
    uint8_t num_params;
} Event_t;

typedef struct {
    const char *pattern;  // Event string pattern
    uint8_t index;        // Unique event identifier
    ParamType param_type; // Indicates whether the handler expects uint8_t or char[]
    union {
        void (*u8)(uint8_t param[MAX_PARAM_LENGTH]);
        void (*chr)(char param[MAX_PARAM_LENGTH][MAX_PARAM_LENGTH]);
        void (*flt)(float param);
        void (*simp)(int param);
    } handler; // Union for different handler types
} command_pattern_t;

typedef struct {
    UART_HandleTypeDef *huart;
    const command_pattern_t *cmdTable;
    const event_pattern_t *evtTable;
} at_device_t;

// Function prototypes
int registerDevice(at_device_t device, uint8_t device_id);
int ParseCommand(char *cmd_str, uint8_t size, uint8_t device_id);
int dispatchCommand(command_t *cmd);

int SendEvent(Event_t *event, uint8_t device_id);
int Send0ParamEvent(uint8_t evt_idx, uint8_t device_id);
int Send1ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param);
int Send2ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param1, uint8_t param2);

#endif // __COMMON_H__

