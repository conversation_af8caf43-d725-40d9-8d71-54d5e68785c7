#ifndef __TLV320ADC_CFG_H__
#define __TLV320ADC_CFG_H__

#include "tlv320adc_enum.h"
#include <stdbool.h>

#define I2C_WRITE_DELAY 10
#define I2C_TIMEOUT 100
#define TLV_RESET_DELAY 300

// Sleep Configurations
#define I2C_ADDRESS_SWAP 0
#define AREG_VOLTAGE Internal_1_8V
#define VREF_CHARGE MS10

// Enable/disable input channels
#define IN_CH_1_EN 
/* #define IN_CH_2_EN  */
//#define IN_CH_3_EN 
//#define IN_CH_4_EN 

// Output Channel Configurations
#define OUT_CH_1_EN 
/* #define OUT_CH_2_EN  */
//#define OUT_CH_3_EN 
//#define OUT_CH_4_EN 

#define OUT_CH1_SLOT L_SLOT_0
#define OUT_CH2_SLOT R_SLOT_0
#define OUT_CH3_SLOT L_SLOT_1
#define OUT_CH4_SLOT R_SLOT_1

// Audio Configurations
#define MODE SLAVE
#define AUTO_CLK_CFG_DISABLE 0
#define PLL_DISABLED 0
#define ASI_FORMAT I2S
#define WORD_LENGTH BIT_16
#define FSYNC_POLARITY Default
#define BCLK_POLARITY Default
#define TX_EDGE Default
#define TX_FILL Default
#define TX_LSB TRANSMIT_FULL_CYCLE
#define TX_KEEPER BUS_KEEPER_ALWAYS_ENABLED
#define TX_OFFSET 0 // 0-31
#define ASI_DAISY_CHAINING_ENABLE 0
#define ASI_ERR_CHECK_DISABLE 0
#define ASI_ERR_RECOVERY_DISABLE 0
#define ASI_MIXING NO_MIXING
#define ASI_MIX_GAIN DB0
#define ASI_INPT_DATA_INVERSE Default
#define MBIAS_VALUE VREF_X_1_096
#define ADC_FULL_SCALE V2_75
#define AGC_ENABLE 0
#define AGC_TARGET_LEVEL AGC_N6DB
#define AGC_MAX_GAIN AGC_3DB
#define OTF_VOLUME_CHANGE_DISABLE 0
#define CH_SUMMATION_EN 0
#define DVOL_CH_CTRL_DISABLE 0
#define SOFT_STEPPING_DISABLE 0

// Master Mode Configurations
#define BCLK_FSYNC_GATE 0
#define FS_MODE FS_48KHZ
#define MCLK_FREQ MCLK_13_MHZ
#define FS_RATE FS_RATE_44_1_48_KHZ
#define FS_BCLK_RATIO FS_BCLK_RATIO_256
#define MASTER_SAMPLE_RATE RATE_48KHZ
#define CLOCK_SOURCE CLK_SRC_BCLK
#define MCLK_FREQ_SELECT_MODE MCLK_FREQ_SELECT_MODE_1
#define MCLK_RATIO_SEL MCLK_RATIO_256
#define INVERT_BCLK_FOR_FSYNC 0

// Filter Configurations
#define ANTI_CLIPPER_ENABLE 1
#define NUMBER_OF_BIQUAD_FILTERS 0
#define DECIMATION_FILTER LINEAR
#define HP_FILTER_SELECTION HPF0_00025

// Power Configurations
#define MIC_BIAS 1
#define ADC 1
#define PLL 1
#define DYNAMIC_CH_PWR_UP_EN 1

// PDM Configurations
#define PDM_CLOCK_DIV PDM_CLOCK_2_8224_MHZ
#define PDM_DIN_EDGE PDMDIN_CH1_NEG_CH2_POS
#define PDM_SAMPLE_RATE PDM_CLOCK_2_8224_MHZ

// GPIO Configurations
#define ENABLE_INTERRUPT 
#define INTERRUPT_POLARITY 1
#define ASI_CLOCK_ERROR_INTERRUPT 1
#define PLL_LOCK_INTERRUPT 1
#define ASI_INPUT_MIXING_SATURATION_INTERRUPT 1
#define VAD_POWER_UP_INTERRUPT 1
#define VAD_POWER_DOWN_INTERRUPT 1

// GPIO Configurations

// GPIO1 (bidirectional pin)
#define GPIO_1_CONFIG    GPIO_PDM_INPUT_1
#define GPIO_1_DRIVE     GPIO_HI_Z_OUTPUT

// GPO1 (push-pull output pin)
#define GPO_1_CONFIG     GPIO_PDMCLK     
#define GPO_1_DRIVE      GPIO_HI_Z_OUTPUT

// GPI pins (dedicated inputs)
#define GPI_1_CONFIG     GPIO_PDM_INPUT_2
#define GPI_2_CONFIG     GPIO_DISABLED

/* Input Channel Configurations */
#ifdef IN_CH_1_EN
// Input Channel 1

#define CH1_INPUT_TYPE INPUT_MICROPHONE
#define CH1_INPUT_CFG ANALOG_DIFFERENTIAL
#define CH1_INPUT_COUPLING AC
#define CH1_IMPEDANCE IMP_2_5K
#define CH1_AGC_ENABLE false 

#define CH1_INPT_GAIN_DB 40 // 0-42
#define CH1_INPT_GAIN_SIGN POSITIVE
#define CH1_INPT_VOLUME 230 // 0-255
#define CH1_GAIN_CALIBRATION ZERO_DB
#define CH1_PHASE_CALIBRATION 0 // 0-255 cycles

#endif // IN_CH_1_EN

#ifdef IN_CH_2_EN
// Input Channel 2

#define CH2_INPUT_TYPE INPUT_MICROPHONE
#define CH2_INPUT_CFG DIGITAL_PDM
#define CH2_INPUT_COUPLING AC
#define CH2_IMPEDANCE IMP_2_5K
#define CH2_AGC_ENABLE false 

#define CH2_INPT_GAIN_DB 42 // 0-42
#define CH2_INPT_GAIN_SIGN POSITIVE
#define CH2_INPT_VOLUME 220 // 0-255
#define CH2_GAIN_CALIBRATION ZERO_DB
#define CH2_PHASE_CALIBRATION 0 // 0-255 cycles

#endif // IN_CH_2_EN

#ifdef IN_CH_3_EN
// Input Channel 3

#define CH3_INPUT_TYPE INPUT_MICROPHONE
#define CH3_INPUT_CFG ANALOG_DIFFERENTIAL
#define CH3_INPUT_COUPLING AC
#define CH3_IMPEDANCE IMP_2_5K
#define CH3_AGC_ENABLE false 

#define CH3_INPT_GAIN_DB 32 // 0-42
#define CH3_INPT_GAIN_SIGN POSITIVE
#define CH3_INPT_VOLUME 220 // 0-255
#define CH3_GAIN_CALIBRATION ZERO_DB
#define CH3_PHASE_CALIBRATION 0 // 0-255 cycles

#endif // IN_CH_3_EN

#ifdef IN_CH_4_EN
// Input Channel 4

#define CH4_INPUT_TYPE INPUT_MICROPHONE
#define CH4_INPUT_CFG ANALOG_DIFFERENTIAL
#define CH4_INPUT_COUPLING AC
#define CH4_IMPEDANCE IMP_2_5K
#define CH4_AGC_ENABLE false 

#define CH4_INPT_GAIN_DB 32 // 0-42
#define CH4_INPT_GAIN_SIGN POSITIVE
#define CH4_INPT_VOLUME 220 // 0-255
#define CH4_GAIN_CALIBRATION ZERO_DB
#define CH4_PHASE_CALIBRATION 0 // 0-255 cycles

#endif // IN_CH_4_EN

// Function Prototypes  
int tlvInit(I2C_HandleTypeDef *hi2c, int filterMode);
int tlvWrite(uint8_t reg, uint8_t data);
int tlvWriteLong(uint8_t reg, uint8_t *data, uint8_t len);
int tlvRead(uint8_t reg, uint8_t *data);
void tlvDBG(const char *format, ...);
void tlvDBGE(const char *format, ...);
void tlvDelay(uint32_t ms);
void setChannels(int direction, int num);
int getChannels(int direction);

#endif // __TLV320ADC_CFG_H__