#include "H5.h"
#include "audio.h"
#include "appEntry.h"
#include "common.h"

const event_pattern_t adc_event_table[] = {
    {"EV+START", EV_ADC_START},
    {"EV+ACK", EV_ADC_ACK},
    {"EV+RDY", EV_ADC_READY},
    {"EV+VOL", EV_ADC_VOLUME},
    {"EV+MUTE", EV_ADC_MUTE},
    {"EV+PWR", EV_ADC_POWER},
    {"EV+FLTR", EV_ADC_FILTER},
    {"EV+AUD", EV_ADC_AUDIO},
    {"EV+BOOT", EV_ADC_BOOT},
    {"EV+STAT", EV_ADC_STATUS},
    {"EV+ERROR", EV_ADC_ERROR},
    {NULL, 0} // End of table marker
};

const command_pattern_t adc_Command_table[] = {
    {"OK", CMD_ADC_ACK, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleAck},
    {"AT+START", CMD_ADC_START, PARAM_TYPE_UINT8, .handler.u8 = ADC_HandleStart},
    {"AT+VLUP", CMD_ADC_INCREASE_VOLUME, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleVolumeUp},
    {"AT+VLDN", CMD_ADC_DECREASE_VOLUME, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleVolumeDown},
    {"AT+MODE", CMD_ADC_SET_MODE, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleMode},
    {"AT+PWR", CMD_ADC_POWER, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandlePower},
    {"AT+STAT", CMD_ADC_STATUS_REQUEST, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleStatus},
    {"AT+MUTE", CMD_ADC_MUTE, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleMute},
    {"AT+UNMUTE", CMD_ADC_UNMUTE, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleUnmute},
    {"AT+FLTR", CMD_ADC_SET_FILTER, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleFilter},
    {"AT+VOL", CMD_ADC_SET_VOLUME, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleVolume},
    {"AT+RST", CMD_ADC_RESET, PARAM_TYPE_SIMPLE, .handler.simp = ADC_HandleReset},
    {NULL, 0, PARAM_TYPE_SIMPLE, .handler.simp = NULL} // End of table marker
};

int ADC_AtmInit(UART_HandleTypeDef *huart) {
    at_device_t atSlave;
    
    atSlave.huart = huart;
    atSlave.cmdTable = adc_Command_table;    // Remove & operator
    atSlave.evtTable = adc_event_table;      // Remove & operator
    
    return registerDevice(atSlave, H5);
}

/*************************************************************************************************************
***************************************Command Functions******************************************************
**************************************************************************************************************/

void ADC_HandleAck(int param) {
    (void)param;
    ackReceivedCallback();
}

void ADC_HandleStart(uint8_t param[MAX_PARAM_LENGTH]) {
    AudioSource_t source;
    source.source = param[0];
    source.mode = param[1];
    source.filter = param[2];
    updateAudioMode(source);
    startCommand(source.source, source.mode, source.filter);
}

void ADC_HandleVolumeUp(int param) {
    (void)param;
    audioIncrementVolume(1);
}

void ADC_HandleVolumeDown(int param) {
    (void)param;
    audioIncrementVolume(0);
}

void ADC_HandleMode(int param) {
    AudioSource_t source;
    source.mode = param;
    updateAudioMode(source);
}

void ADC_HandlePower(int param) {
    if (param != 1) {
        enterStopMode();
    }
}

void ADC_HandleStatus(int param) {
    (void)param;
    ReportStatus();
}

void ADC_HandleMute(int param) {
    (void)param;
    audioMute(1);
}

void ADC_HandleUnmute(int param) {
    (void)param;
    audioMute(0);
}

void ADC_HandleFilter(int param) {
    AudioSource_t source;
    source.filter = param;
    updateAudioMode(source);
}

void ADC_HandleVolume(int param) {
    int IntVolume = param/10;
    float volume = (float)IntVolume;
    audioSetVolume(volume);
}

void ADC_HandleReset(int param) {
    (void)param;
    NVIC_SystemReset();
}

/*************************************************************************************************************
***************************************Event Functions******************************************************
**************************************************************************************************************/

