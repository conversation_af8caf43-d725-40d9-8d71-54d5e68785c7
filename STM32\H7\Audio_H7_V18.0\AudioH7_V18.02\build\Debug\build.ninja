# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.28

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: AudioH7_V18.02
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug/
# =============================================================================
# Object build statements for EXECUTABLE target AudioH7_V18.02


#############################################
# Order-only phony target for AudioH7_V18.02

build cmake_object_order_depends_target_AudioH7_V18.02: phony || cmake_object_order_depends_target_STM32_Drivers

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/main.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/main.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\main.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/gpio.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/gpio.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\gpio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/dma.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/dma.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\dma.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/fmac.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/fmac.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\fmac.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/i2c.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/i2c.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\i2c.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/i2s.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/i2s.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\i2s.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/memorymap.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/memorymap.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\memorymap.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/usart.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/usart.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\usart.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/stm32h7xx_it.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/stm32h7xx_it.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\stm32h7xx_it.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/stm32h7xx_hal_msp.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/stm32h7xx_hal_msp.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\stm32h7xx_hal_msp.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/sysmem.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/sysmem.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\sysmem.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/Core/Src/syscalls.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/syscalls.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Core\Src\syscalls.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Core\Src

build CMakeFiles/AudioH7_V18.02.dir/startup_stm32h725xx.s.obj: ASM_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/startup_stm32h725xx.s || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\startup_stm32h725xx.s.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir

build CMakeFiles/AudioH7_V18.02.dir/App/main/src/appEntry.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/src/appEntry.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\main\src\appEntry.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\main\src

build CMakeFiles/AudioH7_V18.02.dir/App/main/src/audio.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/src/audio.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\main\src\audio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\main\src

build CMakeFiles/AudioH7_V18.02.dir/App/main/src/filterCoeffs.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/src/filterCoeffs.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\main\src\filterCoeffs.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\main\src

build CMakeFiles/AudioH7_V18.02.dir/App/main/src/firInstance.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/src/firInstance.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\main\src\firInstance.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\main\src

build CMakeFiles/AudioH7_V18.02.dir/App/main/src/usbAudio.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/src/usbAudio.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\main\src\usbAudio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\main\src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc/tlv320adc.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc\tlv320adc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc_cfg.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc/tlv320adc_cfg.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc\tlv320adc_cfg.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc_reg.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc/tlv320adc_reg.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc\tlv320adc_reg.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\tlv320adc

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/atSlave/H5.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave/H5.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\atSlave\H5.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\atSlave

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/atSlave/common.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave/common.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\atSlave\common.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\atSlave

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/BasicMathFunctions.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/BasicMathFunctions.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\BasicMathFunctions.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_abs_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_add_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_add_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_dot_prod_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_mult_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_negate_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_offset_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_scale_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_shift_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions\arm_sub_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\BasicMathFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/FilteringFunctions.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/FilteringFunctions.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\FilteringFunctions.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_32x64_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df1_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df2T_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f64.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f64.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df2T_f64.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f64.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f64.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_df2T_init_f64.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_biquad_cascade_stereo_df2T_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_fast_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_opt_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_fast_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_opt_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_partial_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_conv_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_fast_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_opt_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_opt_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_correlate_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_decimate_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_fast_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_fast_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_init_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_interpolate_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_lattice_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_init_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_fir_sparse_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_iir_lattice_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_init_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_init_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_init_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_norm_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions\arm_lms_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\FilteringFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/SupportFunctions.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/SupportFunctions.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\SupportFunctions.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_copy_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_copy_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_copy_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_copy_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_f32.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_f32.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_fill_f32.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_fill_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_fill_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_fill_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_float_to_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_float_to_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_float_to_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_float.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_float.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q15_to_float.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q15_to_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q15_to_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_float.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_float.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q31_to_float.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q31_to_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q7.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q7.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q31_to_q7.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_float.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_float.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q7_to_float.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q15.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q15.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q7_to_q15.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q31.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q31.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions\arm_q7_to_q31.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\Drivers\CMSIS\DSP\Source\SupportFunctions

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/class/src/usbd_audio_if.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/src/usbd_audio_if.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\class\src\usbd_audio_if.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\class\src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/class/src/usbd_audio.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/src/usbd_audio.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\class\src\usbd_audio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\class\src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usb_device.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usb_device.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usb_device.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_conf.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usbd_conf.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usbd_conf.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_core.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usbd_core.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usbd_core.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_ctlreq.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usbd_ctlreq.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usbd_ctlreq.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_desc.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usbd_desc.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usbd_desc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src

build CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_ioreq.c.obj: C_COMPILER__AudioH7_V18.2e02_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Src/usbd_ioreq.c || cmake_object_order_depends_target_AudioH7_V18.02
  DEFINES = -DARM_MATH_CM7 -DARM_MATH_LOOPUNROLL -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src\usbd_ioreq.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  OBJECT_FILE_DIR = CMakeFiles\AudioH7_V18.02.dir\App\drivers\usb\core\Src


# =============================================================================
# Link build statements for EXECUTABLE target AudioH7_V18.02


#############################################
# Link the executable AudioH7_V18.02.elf

build AudioH7_V18.02.elf: C_EXECUTABLE_LINKER__AudioH7_V18.2e02_Debug cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/main.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/gpio.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/dma.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/fmac.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/i2c.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/i2s.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/memorymap.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/usart.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/stm32h7xx_it.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/stm32h7xx_hal_msp.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/sysmem.c.obj CMakeFiles/AudioH7_V18.02.dir/Core/Src/syscalls.c.obj CMakeFiles/AudioH7_V18.02.dir/startup_stm32h725xx.s.obj CMakeFiles/AudioH7_V18.02.dir/App/main/src/appEntry.c.obj CMakeFiles/AudioH7_V18.02.dir/App/main/src/audio.c.obj CMakeFiles/AudioH7_V18.02.dir/App/main/src/filterCoeffs.c.obj CMakeFiles/AudioH7_V18.02.dir/App/main/src/firInstance.c.obj CMakeFiles/AudioH7_V18.02.dir/App/main/src/usbAudio.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc_cfg.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/tlv320adc/tlv320adc_reg.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/atSlave/H5.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/atSlave/common.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/BasicMathFunctions.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/FilteringFunctions.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f64.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f64.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/SupportFunctions.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_copy_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_f32.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_fill_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_float_to_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_float.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q15_to_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_float.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q31_to_q7.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_float.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q15.c.obj CMakeFiles/AudioH7_V18.02.dir/Drivers/CMSIS/DSP/Source/SupportFunctions/arm_q7_to_q31.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/class/src/usbd_audio_if.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/class/src/usbd_audio.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usb_device.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_conf.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_core.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_ctlreq.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_desc.c.obj CMakeFiles/AudioH7_V18.02.dir/App/drivers/usb/core/Src/usbd_ioreq.c.obj || cmake/stm32cubemx/STM32_Drivers
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3
  OBJECT_DIR = CMakeFiles\AudioH7_V18.02.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_FILE = AudioH7_V18.02.elf
  TARGET_PDB = AudioH7_V18.02.elf.dbg
  RSP_FILE = CMakeFiles\AudioH7_V18.02.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake-gui.exe -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/CMakeLists.txt
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target STM32_Drivers


#############################################
# Order-only phony target for STM32_Drivers

build cmake_object_order_depends_target_STM32_Drivers: phony || cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Src/system_stm32h7xx.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src\system_stm32h7xx.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Core\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_cortex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_rcc_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_flash_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_gpio.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_hsem.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_dma_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_mdma.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pwr_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2c_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_exti.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_fmac.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2s.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_i2s_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_uart_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pcd.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_hal_pcd_ex.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src

build cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c.obj: C_COMPILER__STM32_Drivers_unscanned_Debug C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c || cmake_object_order_depends_target_STM32_Drivers
  DEFINES = -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY
  DEP_FILE = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src\stm32h7xx_ll_usb.c.obj.d
  FLAGS = -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11
  INCLUDES = -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc
  OBJECT_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir
  OBJECT_FILE_DIR = cmake\stm32cubemx\CMakeFiles\STM32_Drivers.dir\__\__\Drivers\STM32H7xx_HAL_Driver\Src



#############################################
# Object library STM32_Drivers

build cmake/stm32cubemx/STM32_Drivers: phony cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c.obj cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c.obj


#############################################
# Utility command for edit_cache

build cmake/stm32cubemx/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug\cmake\stm32cubemx && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake-gui.exe -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build cmake/stm32cubemx/edit_cache: phony cmake/stm32cubemx/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build cmake/stm32cubemx/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug\cmake\stm32cubemx && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build cmake/stm32cubemx/rebuild_cache: phony cmake/stm32cubemx/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build App/drivers/usb/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug\App\drivers\usb && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake-gui.exe -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build App/drivers/usb/edit_cache: phony App/drivers/usb/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build App/drivers/usb/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug\App\drivers\usb && C:\ST\STM32CubeCLT_1.16.0\CMake\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02 -BC:\Users\<USER>\OneDrive\Documents\Projects\STM32\H7\Audio_H7_V18.0\AudioH7_V18.02\build\Debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build App/drivers/usb/rebuild_cache: phony App/drivers/usb/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build AudioH7_V18.02: phony AudioH7_V18.02.elf

build STM32_Drivers: phony cmake/stm32cubemx/STM32_Drivers

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug

build all: phony AudioH7_V18.02.elf cmake/stm32cubemx/all App/drivers/usb/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug/App/drivers/usb

build App/drivers/usb/all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug/cmake/stm32cubemx

build cmake/stm32cubemx/all: phony cmake/stm32cubemx/STM32_Drivers

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/cmake/gcc-arm-none-eabi.cmake C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/cmake/stm32cubemx/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.1/CMakeASMCompiler.cmake CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake C$:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/CMakeLists.txt C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/cmake/gcc-arm-none-eabi.cmake C$:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/cmake/stm32cubemx/CMakeLists.txt CMakeCache.txt CMakeFiles/3.28.1/CMakeASMCompiler.cmake CMakeFiles/3.28.1/CMakeCCompiler.cmake CMakeFiles/3.28.1/CMakeCXXCompiler.cmake CMakeFiles/3.28.1/CMakeSystem.cmake: phony


#############################################
# Clean additional files.

build CMakeFiles/clean.additional: CLEAN_ADDITIONAL
  CONFIG = Debug


#############################################
# Clean all the built files.

build clean: CLEAN CMakeFiles/clean.additional


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
