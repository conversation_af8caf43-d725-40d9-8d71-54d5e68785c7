#ifndef H5_H
#define H5_H

#include "common.h"

#define H5 0

#define INITIAL_EVENT_CHAR 'E'
#define INITIAL_CMD_CHAR 'A'
#define INITIAL_DBG_CHAR 'D'
#define MSG_PARAM_SEPERATOR '='
#define PARAM_SEPERATOR ','
#define PARAM_END_CHAR '\r'

typedef enum {
    EV_ADC_START,
    EV_ADC_ACK,
    EV_ADC_READY,
    EV_ADC_VOLUME,
    EV_ADC_MUTE,
    EV_ADC_POWER,
    EV_ADC_FILTER,
    EV_ADC_AUDIO,
    EV_ADC_BOOT,
    EV_ADC_STATUS,
    EV_ADC_ERROR,
    EV_ADC_UNKNOWN,
    END_OF_ADC_EVENTS
} event_type_t;

typedef enum {
    START_OF_ADC_COMMANDS,
    CMD_ADC_ACK,
    CMD_ADC_START,
    CMD_ADC_INCREASE_VOLUME,
    C<PERSON>_ADC_DECREASE_VOLUME,
    CMD_ADC_SET_MODE,
    C<PERSON>_ADC_POWER,
    CMD_ADC_STATUS_REQUEST,
    CMD_ADC_MUTE,
    CMD_ADC_UNMUTE,
    CMD_ADC_SET_FILTER,
    CMD_ADC_SET_VOLUME,
    CMD_ADC_RESET,
    END_OF_ADC_COMMANDS
} command_type_t;

enum ADC_ERROR_CODE {
    ADC_ERROR_NONE = 0,
    ADC_ERROR_UNKNOWN_COMMAND = 1,
    ADC_ERROR_AUDIO_INIT_FAILED = 2,
    ADC_ERROR_AUDIO_START_FAILED = 3,
    ADC_ERROR_ACK_NOT_RECEIVED = 4,
    ADC_ERROR_AUDIO_PROCESSING,
};

enum ADC_ERROR_SUBCODE {
    ADC_ERROR_SUBCODE_NONE = 0,
    ADC_ERROR_SUBCODE_TLV_INIT = 1,
    ADC_ERROR_SUBCODE_I2S_RECEIVE = 2,
    ADC_ERROR_SUBCODE_I2S_TRANSMIT = 3,
    ADC_ERROR_SUBCODE_PROCESS_SLOW = 4,
};

// Command handler function declarations
int ADC_AtmInit(UART_HandleTypeDef *huart);
void ADC_HandleAck(int param);
void ADC_HandleStart(uint8_t param[MAX_PARAM_LENGTH]);
void ADC_HandleVolumeUp(int param);
void ADC_HandleVolumeDown(int param);
void ADC_HandleMode(int param);
void ADC_HandlePower(int param);
void ADC_HandleStatus(int param);
void ADC_HandleMute(int param);
void ADC_HandleUnmute(int param);
void ADC_HandleFilter(int param);
void ADC_HandleVolume(int param);
void ADC_HandleReset(int param);

#endif // H5_H
