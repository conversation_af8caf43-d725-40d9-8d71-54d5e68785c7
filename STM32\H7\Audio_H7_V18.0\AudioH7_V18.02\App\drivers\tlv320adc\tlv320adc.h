#ifndef __TLV320ADC_H__
#define __TLV320ADC_H__

#include "stm32h7xx_hal.h"
#include "tlv320adc_reg.h"
#include "tlv320adc_cfg.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef I2C_PIN_ENABLED
#define TLV320ADC_I2C_ADDR (0x4c)
#else
#define TLV320ADC_I2C_ADDR (0x9c)
#define TLV320ADC_I2C_READ_ADDR (0x9d)
#endif

#define SLEEP_CFG_MASK (0x80 | 0x08 | 0x04 | 0x01)  // 0x8D
#define IN_CH_CFG0_MASK (0x80 | 0x60 | 0x10 | 0x0C)  // 0x8D

#define NUM_FILTER_MODES 0
#define MAX_NUM_BIQUADS 0
#define MAX_NUM_CHANNELS 4


typedef struct {
    I2C_HandleTypeDef *i2c;
    uint8_t addr;
    uint8_t OutChs;
    uint8_t InChs;
    int errors;
} tlv320adc_t;

typedef struct {
    uint8_t addr;
    uint8_t value;
    
} reg_cfg_t;

typedef struct {
    uint8_t impedance;
    uint8_t coupling;
    uint8_t source;
    uint8_t type;
    bool AGC_enabled;
    uint8_t gain_db;
    uint8_t gain_sign;
    uint8_t DigitalVolume; // 0-255
    uint8_t gainCal;
    uint8_t phaseCal; // 0-255
} InputChannelCFG;

typedef struct
{
    int32_t N0;
    int32_t N1;
    int32_t N2;
    int32_t D1;
    int32_t D2;
} FilterCoefficients;

typedef struct {
    uint8_t CH1;
    uint8_t CH2;
    uint8_t device_mode;
    uint8_t detected_fs_rate;
    uint8_t detected_fs_ratio;
    uint8_t ErrorPllLock;
    uint8_t ErrorASIClock;
} tlvStatus_t;

/// Funcction Prototypes

int tlvReset(void);
int tlvSleep(int sleep);
int configureAudio(void);
int configureInputChannel(uint8_t *enData);
int EnableInputChannel(uint8_t channel, InputChannelCFG *cfg);
int configureGPIO(void);
int configureOutputChannel(uint8_t *enData);
void tlvInitFilter(void);
int configureFilter(int filterMode);
int tlvSetFilter(uint8_t filterNum, const FilterCoefficients *coeffs);
int tlvEnableChannels(uint8_t in_ch, uint8_t out_ch);
int tlvPowerUp(void);

#endif // __TLV320ADC_H__