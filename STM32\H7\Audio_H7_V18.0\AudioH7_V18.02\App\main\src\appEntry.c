#include "appEntry.h"
#include "audio.h"
#include "H5.h"

static AudioSource_t audio = {0};
__attribute__((section(".RAM_D2")))
__attribute__((aligned(32))) char ATM_RxBuffer[UART_RX_BUFFER_SIZE];
int AudioState = audioStateWaiting;
static uint8_t startReceivedFlag = 0;

const AudioSource_t *getAudioSource(void)
{
    return &audio;
}

void systemPeripheralInit(void)
{
    audio.I2C = &TLV_I2C;
    ADC_AtmInit(&MCU_UART);
    memset(ATM_RxBuffer, 0, CMD_BUFFER_SIZE);
    HAL_UARTEx_ReceiveToIdle_DMA(&MCU_UART, (uint8_t *)ATM_RxBuffer, UART_RX_BUFFER_SIZE);
    HAL_Delay(100);
}

void systemAudioInit(void)
{
    uint8_t audioStarted = 0;
    int err = 0;
    audio.volume = 3.0f;
    while (audioStarted == 0)
    {
        switch (AudioState)
        {
        case audioStateWaiting:
            audio.state = AUDIO_STATE_INIT;
            Send1ParamEvent(EV_ADC_POWER, H5, 1);
            HAL_Delay(1000);

            if (startReceivedFlag == 1)
            {
                AudioState = audioStateInit;
            }
            break;
        case audioStateInit:
            err = audioSourceInit(&audio);
            if (err != 0)
            {
                printf("Audio source init failed\r\n");
                AudioState = audioStateWaiting;
            }
            else
            {
                AudioState = audioStateStart;
            }
            break;
        case audioStateStart:
            err = audioStart(&audio);
            break;
        }
    }
}

int startCommand(int source, int mode, int filter)
{
    switch (source)
    {
    case sourceSteth:
        audio.source = sourceSteth;
        audio.InI2S = &TLV_I2S;
        break;
    case sourceMic:
        audio.source = sourceMic;
        audio.InI2S = &TLV_I2S;
        break;
    }

    switch (mode)
    {
    case modeA2DP:
        audio.mode = modeA2DP;
        audio.OutI2S = &BTM_I2S;
        break;
    case modeBLE:
        audio.mode = modeBLE;
        audio.OutI2S = &MCU_I2S;
        break;
    case modeUSB:
        audio.mode = modeUSB;
        audio.OutI2S = &MCU_I2S;
        break;
    }
    audio.filter = filter;
    startReceivedFlag = 1;
    return 0;
}

void updateAudioMode(AudioSource_t Newmode)
{
    if (Newmode.mode != audio.mode)
    {
        audio.mode = Newmode.mode;
        audio.filter = Newmode.filter;
        audio.InI2S = Newmode.InI2S;
        audio.OutI2S = Newmode.OutI2S;
    }
}

void enterStopMode(void)
{
     tlvSleep(0);
     HAL_StatusTypeDef status;
     status = HAL_I2S_DMAStop(audio.InI2S);
     if (status != HAL_OK)
     {
         printf("Error Stopping I2S receive DMA\r\n");
     }
     status = HAL_I2S_DMAStop(audio.OutI2S);
     if (status != HAL_OK)
     {
         printf("Error Stopping I2S transmit DMA\r\n");
     }
     status = HAL_UART_AbortReceive_IT(&MCU_UART);
     if (status != HAL_OK) {
         printf("Error Aborting UART receive\r\n");
     }

     HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);
}

void exitStopMode(void)
{
    HAL_StatusTypeDef status;
    SystemClock_Config();
    HAL_Delay(100);
    tlvSleep(1);
    status = HAL_UARTEx_ReceiveToIdle_DMA(&MCU_UART, (uint8_t *)ATM_RxBuffer, UART_RX_BUFFER_SIZE);
    if (status != HAL_OK) {
        printf("Error Starting UART receive\r\n");
    }
}

void ReportStatus(void)
{
    Send1ParamEvent(EV_ADC_AUDIO, H5, audio.state);
    HAL_Delay(100);
    Send4ParamEvent(EV_ADC_STATUS, H5, audio.source, audio.mode, audio.filter, audio.volume);
}

void updateAudioState(int state, int send)
{
    audio.state = state;
    if (send == 1)
    {
        Send1ParamEvent(EV_ADC_AUDIO, H5, audio.state);
    }
}

void audioIncrementVolume(int direction)
{
    if (direction == 1)
    {
        if (audio.volume < 10.0f)
        {
            audio.volume += 0.1f;
        }
    }
    else
    {
        if (audio.volume > 0.0f)
        {
            audio.volume -= 0.1f;
        }
    }
    ReportVolume();
}

void audioSetVolume(float volume)
{
    audio.volume = volume;
    ReportVolume();
}

void ReportVolume(void)
{
    int volume = (int)(audio.volume * 10);
    Send1ParamEvent(EV_ADC_VOLUME, H5, volume);
}

void audioMute(int mute)
{
    static float previousVolume = 0.0f;
    if (mute == 1)
    {
        if (audio.mute == 0)
        {
            previousVolume = audio.volume;
            audio.mute = 1;
            audio.volume = 0.0f;
        }
    }
    else
    {
       if (audio.mute == 1)
        {
            audio.volume = previousVolume;
            audio.mute = 0;
        }
    }
    Send1ParamEvent(EV_ADC_MUTE, H5, audio.mute);
}

int __io_putchar(int ch)
{
    HAL_UART_Transmit(&DBG_UART, (uint8_t *)&ch, 1, 100);
    return ch;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
    if (huart == &MCU_UART)
    {
        ParseCommand(ATM_RxBuffer, Size, H5);
        memset(ATM_RxBuffer, 0, CMD_BUFFER_SIZE);
        HAL_UARTEx_ReceiveToIdle_DMA(&MCU_UART, (uint8_t *)ATM_RxBuffer, UART_RX_BUFFER_SIZE);
    }
}

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart)
{
    if (huart == &MCU_UART)
    {
        HAL_UART_AbortReceive_IT(&MCU_UART);
        HAL_UARTEx_ReceiveToIdle_DMA(&MCU_UART, (uint8_t *)ATM_RxBuffer, UART_RX_BUFFER_SIZE);
    }
}