{"artifacts": [{"path": "AudioH7_V18.02.elf"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_compile_definitions", "target_include_directories", "target_sources"], "files": ["CMakeLists.txt", "cmake/stm32cubemx/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 38, "parent": 0}, {"file": 1}, {"command": 1, "file": 1, "line": 99, "parent": 2}, {"command": 2, "file": 0, "line": 78, "parent": 0}, {"command": 3, "file": 0, "line": 69, "parent": 0}, {"command": 1, "file": 0, "line": 84, "parent": 0}, {"command": 4, "file": 1, "line": 93, "parent": 2}, {"command": 4, "file": 0, "line": 61, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11"}], "defines": [{"backtrace": 4, "define": "ARM_MATH_CM7"}, {"backtrace": 3, "define": "DEBUG"}, {"backtrace": 3, "define": "STM32H725xx"}, {"backtrace": 3, "define": "USE_HAL_DRIVER"}, {"backtrace": 3, "define": "USE_PWR_DIRECT_SMPS_SUPPLY"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc"}, {"backtrace": 6, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc"}, {"backtrace": 6, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166]}, {"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g"}], "defines": [{"backtrace": 4, "define": "ARM_MATH_CM7"}, {"backtrace": 3, "define": "DEBUG"}, {"backtrace": 3, "define": "STM32H725xx"}, {"backtrace": 3, "define": "USE_HAL_DRIVER"}, {"backtrace": 3, "define": "USE_PWR_DIRECT_SMPS_SUPPLY"}], "includes": [{"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include"}, {"backtrace": 5, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include"}, {"backtrace": 3, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc"}, {"backtrace": 6, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc"}, {"backtrace": 6, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc"}], "language": "ASM", "sourceIndexes": [12]}], "dependencies": [{"backtrace": 3, "id": "STM32_Drivers::@768a070a0fe75716b479"}], "id": "AudioH7_V18.02::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "C"}, "name": "AudioH7_V18.02", "nameOnDisk": "AudioH7_V18.02.elf", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166]}, {"name": "", "sourceIndexes": [12]}, {"name": "Object Libraries", "sourceIndexes": [167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191]}], "sources": [{"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/main.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/gpio.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/dma.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/fmac.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/i2c.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/i2s.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/memorymap.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/usart.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/stm32h7xx_it.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/stm32h7xx_hal_msp.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/sysmem.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 0, "path": "Core/Src/syscalls.c", "sourceGroupIndex": 0}, {"backtrace": 7, "compileGroupIndex": 1, "path": "startup_stm32h725xx.s", "sourceGroupIndex": 1}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/main/src/appEntry.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/main/src/audio.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/main/src/filterCoeffs.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/main/src/firInstance.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/main/src/usbAudio.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/drivers/tlv320adc/tlv320adc.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/drivers/tlv320adc/tlv320adc_cfg.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/drivers/tlv320adc/tlv320adc_reg.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/drivers/atSlave/H5.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "App/drivers/atSlave/common.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/BasicMathFunctions.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_abs_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_add_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_dot_prod_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_mult_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_negate_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_offset_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_scale_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_shift_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/BasicMathFunctions/arm_sub_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/FilteringFunctions.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_32x64_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df1_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_f64.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_df2T_init_f64.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_biquad_cascade_stereo_df2T_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_opt_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_opt_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_partial_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_conv_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_opt_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_correlate_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_decimate_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_fast_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_init_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_interpolate_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_lattice_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_init_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_fir_sparse_q7.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_iir_lattice_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_f32.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_init_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_norm_q31.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q15.c", "sourceGroupIndex": 0}, {"backtrace": 8, "compileGroupIndex": 0, "path": "Drivers/CMSIS/DSP/Source/FilteringFunctions/arm_lms_q31.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/class/src/usbd_audio_if.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/class/src/usbd_audio.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usb_device.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usbd_conf.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usbd_core.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usbd_ctlreq.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usbd_desc.c", "sourceGroupIndex": 0}, {"backtrace": 6, "compileGroupIndex": 0, "path": "App/drivers/usb/core/Src/usbd_ioreq.c", "sourceGroupIndex": 0}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Core/Src/system_stm32h7xx.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c.obj", "sourceGroupIndex": 2}, {"backtrace": 3, "isGenerated": true, "path": "build/Debug/cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c.obj", "sourceGroupIndex": 2}], "type": "EXECUTABLE"}