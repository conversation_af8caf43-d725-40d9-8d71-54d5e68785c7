{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.22"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "cmake/stm32cubemx", "jsonFile": "directory-cmake.stm32cubemx-Debug-a874d3cc84b5982fda2e.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 0, "projectIndex": 0, "source": "cmake/stm32cubemx", "targetIndexes": [1]}, {"build": "App/drivers/usb", "jsonFile": "directory-App.drivers.usb-Debug-a11ad5256e4ae8b553bb.json", "minimumCMakeVersion": {"string": "3.22"}, "parentIndex": 0, "projectIndex": 0, "source": "App/drivers/usb"}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2], "name": "AudioH7_V18.02", "targetIndexes": [0, 1]}], "targets": [{"directoryIndex": 0, "id": "AudioH7_V18.02::@6890427a1f51a3e7e1df", "jsonFile": "target-AudioH7_V18.02-Debug-4b54d234732a46cc0d97.json", "name": "AudioH7_V18.02", "projectIndex": 0}, {"directoryIndex": 1, "id": "STM32_Drivers::@768a070a0fe75716b479", "jsonFile": "target-STM32_Drivers-Debug-063db83e8aa8e56db3d4.json", "name": "STM32_Drivers", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "source": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02"}, "version": {"major": 2, "minor": 6}}