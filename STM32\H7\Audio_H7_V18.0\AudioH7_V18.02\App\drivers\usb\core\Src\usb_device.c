#include "usb_device.h"
#include "usbd_core.h"
#include "usbd_desc.h"
#include "usbd_audio.h"
#include "usbd_audio_if.h"
#include "main.h"

USBD_HandleTypeDef hUsbDeviceHS;

void MX_USB_DEVICE_Init() {

  USBD_AUDIO_Init_Microphone_Descriptor(&hUsbDeviceHS, MIC_SAMPLE_FREQUENCY, MIC_NUM_CHANNELS);

  /* Init Device Library, add supported class and start the library. */

  if (USBD_Init(&hUsbDeviceHS, &HS_Desc, DEVICE_HS) != USBD_OK ||
      USBD_RegisterClass(&hUsbDeviceHS, &USBD_AUDIO) != USBD_OK ||
      USBD_AUDIO_RegisterInterface(&hUsbDeviceHS, &USBD_AUDIO_fops) != USBD_OK ||
      USBD_Start(&hUsbDeviceHS) != USBD_OK) {
    Error_Handler();
  }
}
