# CMakeLists.txt for USB Device Library

add_library(usb_device INTERFACE)

# Add source files to the library
target_sources(usb_device INTERFACE
    ${CMAKE_CURRENT_LIST_DIR}/class/src/usbd_audio_if.c
    ${CMAKE_CURRENT_LIST_DIR}/class/src/usbd_audio.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usb_device.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usbd_conf.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usbd_core.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usbd_ctlreq.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usbd_desc.c
    ${CMAKE_CURRENT_LIST_DIR}/core/src/usbd_ioreq.c
)

# Add include directories
target_include_directories(usb_device INTERFACE
    ${CMAKE_CURRENT_LIST_DIR}/class/inc
    ${CMAKE_CURRENT_LIST_DIR}/core/Inc
)

