#ifndef FIR_INSTANCE_H
#define FIR_INSTANCE_H

#include "stm32h7xx_hal.h"
#include "audio.h"

#ifndef _ARM_MATH_CM7
#define _ARM_MATH_CM7
#endif

#ifndef _ARM_MATH_H
#include "arm_math.h"
#endif

#define FIR_KERNAL_GAIN 1.00f
#define MAX_TAPS 351
#define FIR_BLOCK_LENGTH (AUDIO_BUFFER_SIZE / 4)

#define INT16_TO_FLOAT (1.0f / 32768.0f)
#define FLOAT_TO_INT16 (32768.0f)

typedef struct {
    arm_fir_instance_f32 firInstance;
    float *firBuf;              // FIR state buffer: [numTaps + blockSize - 1]
    float *kernel;              // Pointer to filter coefficients
    float *out;        // Output buffer
    uint16_t numTaps;
} FirContainer_t;


/**
 * @brief Initialize FIR filter instance
 * @param fir Pointer to FirContainer_t structure to initialize
 * @param coeffs Pointer to filter coefficients array
 * @param numTaps Number of filter taps (coefficients)
 * @param stateBuf Pointer to state buffer for filter delay line
 */
void FirInit(FirContainer_t *fir, const float *coeffs, uint16_t numTaps, float *stateBuf, float *outBuffer);

/**
 * @brief Update FIR filter with new input data
 * @param c Pointer to FirContainer_t structure
 * @param inp Pointer to input float array of length FIR_BLOCK_LENGTH
 * @note Output is stored in c->out array
 */
void FirUpdate(FirContainer_t *c, float *inp);


#endif // FIR_INSTANCE_H