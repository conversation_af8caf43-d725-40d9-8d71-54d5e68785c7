/**
  ******************************************************************************
  * @file    usbd_cdc_if_template.c
  * <AUTHOR> Application Team
  * @brief   Generic media access Layer.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2015 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "usbd_audio_if.h"
#include "usbAudio.h"
#include <stdbool.h>

/** @addtogroup STM32_USB_DEVICE_LIBRARY
  * @{
  */


/** @defgroup USBD_AUDIO
  * @brief usbd core module
  * @{
  */

/** @defgroup USBD_AUDIO_Private_TypesDefinitions
  * @{
  */
/**
  * @}
  */


/** @defgroup USBD_AUDIO_Private_Defines
  * @{
  */
/**
  * @}
  */


/** @defgroup USBD_AUDIO_Private_Macros
  * @{
  */

/**
  * @}
  */


/** @defgroup USBD_AUDIO_Private_FunctionPrototypes
  * @{
  */

  static int8_t Audio_Init(uint32_t AudioFreq, uint32_t BitRes, uint32_t ChnlNbr);
  static int8_t Audio_DeInit(uint32_t options);
  static int8_t Audio_Record();
  static int8_t Audio_VolumeCtl(uint8_t Volume);
  static int8_t Audio_MuteCtl(uint8_t cmd);
  static int8_t Audio_Stop();
  static int8_t Audio_Pause();
  static int8_t Audio_Resume();
  static int8_t Audio_CommandMgr(uint8_t cmd);

USBD_AUDIO_ItfTypeDef USBD_AUDIO_fops =
{
  Audio_Init,
  Audio_DeInit,
  Audio_Record,
  Audio_VolumeCtl,
  Audio_MuteCtl,
  Audio_Stop,
  Audio_Pause,
  Audio_Resume,
  Audio_CommandMgr
};

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Audio_Init
  *         Initializes the AUDIO media low layer
  * @param  None
  * @retval Result of the operation: USBD_OK if all operations are OK else USBD_FAIL
  */
static int8_t Audio_Init(uint32_t  AudioFreq, uint32_t Volume, uint32_t options)
{
  UNUSED(AudioFreq);
  UNUSED(Volume);
  UNUSED(options);

  /*
     Add your initialization code here
  */
  return USBD_OK;
}

/**
  * @brief  Audio_DeInit
  *         DeInitializes the AUDIO media low layer
  * @param  None
  * @retval Result of the operation: USBD_OK if all operations are OK else USBD_FAIL
  */
static int8_t Audio_DeInit(uint32_t options)
{
  UNUSED(options);

  /*
     Add your deinitialization code here
  */
  return USBD_OK;
}


/**
  * @brief  Audio Record
  *         Start audio recording engine
  * @param  Buf: Buffer of data to be sent
  * @param  size: Number of data to be sent (in bytes)
  * @param  cmd: command opcode
  * @retval Result of the operation: USBD_OK if all operations are OK else USBD_FAIL
  */
 static int8_t Audio_Record() 
{
  if (usbAudioStart() == HAL_OK)
  {
    return USBD_OK;
  } else {
    return USBD_FAIL;
  }
}

/**
  * @brief  Audio_VolumeCtl
  * @param  vol: volume level (0..100)
  * @retval Result of the operation: USBD_OK if all operations are OK else USBD_FAIL
  */
static int8_t Audio_VolumeCtl(uint8_t vol)
{
  UNUSED(vol);
  
  return USBD_OK;
}

/**
  * @brief  Audio_MuteCtl
  * @param  cmd: vmute command
  * @retval Result of the operation: USBD_OK if all operations are OK else USBD_FAIL
  */
static int8_t Audio_MuteCtl(uint8_t cmd)
{
  UNUSED(cmd);
  static bool mute;
  mute = !mute;
  usbAudioMute(mute);
  return USBD_OK;
}

/**
 * @brief  Stops audio acquisition
 * @param  none
 * @retval BSP_ERROR_NONE in case of success, AUDIO_ERROR otherwise
 */

 static int8_t Audio_Stop() {
  
  return usbAudioStop();
}

/**
 * @brief  Pauses audio acquisition
 * @param  none
 * @retval BSP_ERROR_NONE in case of success, AUDIO_ERROR otherwise
 */

 static int8_t Audio_Pause() 
 {
  
  return usbAudioPause();
}

/**
 * @brief  Resumes audio acquisition
 * @param  none
 * @retval BSP_ERROR_NONE in case of success, AUDIO_ERROR otherwise
 */

static int8_t Audio_Resume() 
{
  
  return usbAudioResume();
}

/**
 * @brief  Manages command from usb
 * @param  None
 * @retval BSP_ERROR_NONE in case of success, AUDIO_ERROR otherwise
 */

static int8_t Audio_CommandMgr(uint8_t cmd) {
  UNUSED(cmd);
  return USBD_OK;
}

