#ifndef _FILTERS_H
#define _FILTERS_H

#include "tlv320adc.h"


FilterCoefficients filterCoeffs[NUM_FILTER_MODES][MAX_NUM_BIQUADS]; 

const FilterCoefficients None = {
    0,
    0,
    0,
    0,
    0};

/****************************************************************************************************************************
 * Balanced Filter Coefficients
 *******************************************************************************************************************************/

// EQ, 44.1khz, 100hz, 10db, BW 150, q1, 32 bit
const FilterCoefficients EQBalanced32 = {
    0x7FFFFFFF,
    0x8B5FD953,
    0x69A16742,
    0x7A8D03B2,
    0x8A7FEFC7};

const FilterCoefficients EQ2Balanced32 = {
    0x7FFFFFFF,
    0x8B5FD953,
    0x69A16742,
    0x7A8D03B2,
    0x8A7FEFC7};

// LowPass, BW2, 44.1khz, 200hz, 10db, q1, 24 bit
const FilterCoefficients LowPassB2Balanced32 = {
    0x0096DC9B,
    0x0096DC9B,
    0x0096DC9B,
    0x7323FD46,
    0x975C9306};

/****************************************************************************************************************************
 * Lung Filter Coefficients
 *******************************************************************************************************************************/

// Notch, 44.1khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients NotchLung32 = {
    0x7FFFFF,
    0x800D4F,
    0x7FFFFF,
    0x7FE009,
    0x802555};

// EQ, 44.1khz, 600hz, 7db, BW 400, q1, 32 bit
const FilterCoefficients EQLung32 = {
    0x7FFFFFFF,
    0x881DFCE1,
    0x70A4FC29,
    0x7BFF8F2B,
    0x871832C3};

// Bass Shelf, 44.1khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients BassShelfLung32 = {
    0x7FFFFFFF,
    0x88EB82AC,
    0x6F532467,
    0x794DF04B,
    0x8CB980FB};

// LowPass, variableQ2, 44.1khz, 200hz, 10db, q1, 24 bit
const FilterCoefficients LowPassVLung32 = {
    0x0066094F,
    0x0066094F,
    0x0066094F,
    0x7A4FA064,
    0x8A7B3AD8};

// LowPass, BW2, 44.1khz, 200hz, 10db, q1, 24 bit
const FilterCoefficients LowPassB2Lung32 = {
    0x0096DC9B,
    0x0096DC9B,
    0x0096DC9B,
    0x7323FD46,
    0x975C9306};

// HighPass, BW2, 44.1khz, 100hz, 10db, q1, 24 bit
const FilterCoefficients HighPassB2Lung32 = {
    0x7FFFFFFF,
    0x80000001,
    0x7FFFFFFF,
    0x7EB5D6BB,
    0x828DBCB7};

// Notch, 16khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients NotchLung16 = {
    0x7FFFFFFF,
    0x80194352,
    0x7FFFFFFF,
    0x7D7022BC,
    0x84EE2D02};

// EQ, 16khz, 600hz, 12db, BW 400, q1, 32 bit
const FilterCoefficients EQLung16 = {
    0x7FFFFFFF,
    0xA13AD315,
    0x42ED1F79,
    0x7361EE8F,
    0x92AD7BEF};

// Bass Shelf, 16khz, 200hz, 12db, q1, 32 bit
const FilterCoefficients BassShelfLung16 = {
    0x7FFFFFFF,
    0xA7C3965B,
    0x43E22CF9,
    0x6B842C36,
    0xA342D225};

// LowPass, variableQ2, 16khz, 600hz, 0db, q1, 32 bit
const FilterCoefficients LowPassVLung16 = {
    0x01955FF2,
    0x01955FF2,
    0x01955FF2,
    0x6F74418E,
    0x9AC1FD19};

// LowPass, BW2, 16khz, 600hz, 0db, q0.707, 24 bit
const FilterCoefficients LowPassB2Lung16 = {
    0x01848B52,
    0x01848B52,
    0x01848B52,
    0x6AD3A39A,
    0xA4468B81};

// HighPass, VQ2, 16khz, 200hz, 12db, q1, 32 bit
const FilterCoefficients HighPassvLung16 = {
    0x7AFA7385,
    0x85058C7B,
    0x7AFA7385,
    0x7AC9D9F5,
    0x89A9E5D5};

/****************************************************************************************************************************
 * Heart Filter Coefficients
 *******************************************************************************************************************************/

// Notch, 44.1khz, 200hz, 10db, q1, 24 bit
const FilterCoefficients NotchHeart32 = {
    0x7FFFFF,
    0x800D4F,
    0x7FFFFF,
    0x7FE009,
    0x802555};

// EQ, 44.1khz, 150hz, 7db, BW150, q1, 32 bit
/* const FilterCoefficients EQHeart32 = {
0x7FFFFFFF,
0x8304E855,
0x7A04CD10,
0x7E9E22F3,
0x82B4EB5E
}; */

// EQ, 44.1khz, 100hz, 12db, BW100, q1, 32 bit
const FilterCoefficients EQHeart32 = {
    0x7FFFFFFF,
    0x838AEE78,
    0x78F09AE9,
    0x7F14E99C,
    0x81CF9204};

// Bass Shelf, 44.1khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients BassShelfHeart32 = {
    0x7FFFFF,
    0x8455DD,
    0x779D0A,
    0x7D8FA4,
    0x84C95D};

// LowPass, variableQ2, 44.1khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients LowPassVHeart32 = {
    0x00149E1E,
    0x00149E1E,
    0x00149E1E,
    0x7D6BBE87,
    0x850E6E96};

// LowPass, Bessel 2, 44.1khz, 200hz, 0db, q0.58, 32 bit
const FilterCoefficients LowPassBessHeart32 = {
    0x00067DE6,
    0x00067DE6,
    0x00067DE6,
    0x7CE17AD3,
    0x862312BE};

// LowPass, BW2, 44.1khz, 400hz, 0db, q0.7, 32 bit
const FilterCoefficients LowPassB2Heart32 = {
    0x001991D0,
    0x001991D0,
    0x001991D0,
    0x7AD7FEF6,
    0x89E9BAD2};

// EQ, 16khz, 100hz, 12db, BW100, q1, 32 bit
const FilterCoefficients EQHeart16 = {
    0x7FFFFFFF,
    0x895F6C7E,
    0x6D7003C3,
    0x7D7022BC,
    0x84EE2D02};

// Bass Shelf, 16khz, 200hz, 10db, q1, 32 bit
const FilterCoefficients BassShelfHeart16 = {
    0x7FFFFFFF,
    0x8BE9EE23,
    0x6A3571F8,
    0x79484E33,
    0x8CC3AC71};

// LowPass, variableQ2, 16khz, 200hz, 0db, q1, 32 bit
const FilterCoefficients LowPassVHeart16 = {
    0x00309990,
    0x00309990,
    0x00309990,
    0x7AC9D9F5,
    0x89A9E5D5};

// LowPass, Bessel 2, 16khz, 200hz, 0db, q0.58, 32 bit
const FilterCoefficients LowPassBessHeart16 = {
    0x002F4E84,
    0x002F4E84,
    0x002F4E84,
    0x77857645,
    0x9037D961};

// LowPass, BW2, 16khz, 400hz, 0db, q0.7, 32 bit
const FilterCoefficients LowPassB2Heart16 = {
    0x002FD9EC,
    0x002FD9EC,
    0x002FD9EC,
    0x78E5AC18,
    0x8D75401C};

#endif /* FILTER_H_ */

