#ifndef APPENTRY_H
#define APPENTRY_H

#include "i2c.h"
#include "i2s.h"
#include "usart.h"
#include "audio.h"
#include <stdio.h>

#define TLV_I2C hi2c3

#define TLV_I2S hi2s2
#define BTM_I2S hi2s3
#define MCU_I2S hi2s1

#define MCU_UART huart2
#define DBG_UART huart4

#define UART_RX_BUFFER_SIZE 64

enum audio_state {
    audioStateWaiting,
    audioStateInit,
    audioStateStart,
    audioStateRunning,
    audioStateError,
};

/**
 * @brief Get pointer to the audio source configuration
 * @return Const pointer to the global AudioSource_t structure
 */
const AudioSource_t *getAudioSource(void);

/**
 * @brief Initialize system peripherals including I2C, UART, and H5 protocol
 * @note Sets up TLV I2C interface, H5 communication protocol, and starts
 *       UART receive in interrupt mode with idle detection
 */
void systemPeripheralInit(void);

/**
 * @brief Initialize and start the audio system
 * @note Implements state machine for audio initialization:
 *       - Waits for start command
 *       - Initializes audio source
 *       - Starts audio processing (with or without FIR filter)
 */
void systemAudioInit(void);

/**
 * @brief Configure audio source, mode, and filter based on received command
 * @param source Audio input source (sourceSteth or sourceMic)
 * @param mode Audio output mode (modeA2DP, modeBLE, or modeUSB)
 * @param filter Filter type to apply (BALANCED_MODE, LUNG_MODE, HEART_MODE, or NO_FILTER)
 * @return 0 on success
 * @note Sets the startReceivedFlag to trigger audio initialization
 */
int startCommand(int source, int mode, int filter);

void updateAudioMode(AudioSource_t Newmode);

void enterStopMode(void);

void exitStopMode(void);

void ReportStatus(void);

void updateAudioState(int state, int send);

void audioIncrementVolume(int direction);

void audioSetVolume(float volume);

void ReportVolume(void);

void audioMute(int mute);

int __io_putchar(int ch);

void HAL_UARTEx_RxCpltCallback(UART_HandleTypeDef *huart, uint16_t Size);

void HAL_UART_ErrorCallback(UART_HandleTypeDef *huart);

#endif // APPENTRY_H