[{"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\main.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\main.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\main.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\main.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\gpio.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\gpio.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\gpio.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\gpio.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\dma.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\dma.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\dma.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\dma.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\fmac.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\fmac.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\fmac.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\fmac.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\i2c.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\i2c.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\i2c.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\i2c.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\i2s.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\i2s.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\i2s.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\i2s.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\memorymap.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\memorymap.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\memorymap.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\memorymap.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\usart.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\usart.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\usart.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\usart.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\stm32h7xx_it.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\stm32h7xx_it.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\stm32h7xx_it.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\stm32h7xx_it.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\stm32h7xx_hal_msp.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\stm32h7xx_hal_msp.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\stm32h7xx_hal_msp.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\stm32h7xx_hal_msp.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\sysmem.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\sysmem.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\sysmem.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\sysmem.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\syscalls.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\syscalls.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\syscalls.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Core\\Src\\syscalls.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -x assembler-with-cpp -MMD -MP -g -o CMakeFiles\\AudioH7_V18.02.dir\\startup_stm32h725xx.s.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\startup_stm32h725xx.s", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\startup_stm32h725xx.s", "output": "CMakeFiles\\AudioH7_V18.02.dir\\startup_stm32h725xx.s.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\appEntry.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\appEntry.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\appEntry.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\appEntry.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\audio.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\audio.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\audio.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\audio.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\filterCoeffs.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\filterCoeffs.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\filterCoeffs.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\filterCoeffs.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\firInstance.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\firInstance.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\firInstance.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\firInstance.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\usbAudio.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\usbAudio.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\main\\src\\usbAudio.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\main\\src\\usbAudio.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc_cfg.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc_cfg.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc_cfg.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc_cfg.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc_reg.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc_reg.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\tlv320adc\\tlv320adc_reg.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\tlv320adc\\tlv320adc_reg.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\atSlave\\H5.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\atSlave\\H5.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\atSlave\\H5.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\atSlave\\H5.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\atSlave\\common.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\atSlave\\common.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\atSlave\\common.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\atSlave\\common.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\BasicMathFunctions.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\BasicMathFunctions.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\BasicMathFunctions.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\BasicMathFunctions.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_abs_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_add_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_dot_prod_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_mult_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_negate_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_offset_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_scale_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_shift_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\BasicMathFunctions\\arm_sub_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\FilteringFunctions.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\FilteringFunctions.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\FilteringFunctions.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\FilteringFunctions.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_32x64_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df1_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f64.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f64.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f64.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_f64.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f64.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f64.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f64.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_df2T_init_f64.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_biquad_cascade_stereo_df2T_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_opt_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_opt_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_partial_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_conv_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_opt_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_correlate_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_decimate_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_fast_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_init_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_interpolate_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_lattice_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_init_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q7.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q7.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q7.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_fir_sparse_q7.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_iir_lattice_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_f32.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_f32.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_f32.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_f32.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_init_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_norm_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q15.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q15.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q15.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q15.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q31.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q31.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q31.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\Drivers\\CMSIS\\DSP\\Source\\FilteringFunctions\\arm_lms_q31.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\class\\src\\usbd_audio_if.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\class\\src\\usbd_audio_if.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\class\\src\\usbd_audio_if.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\class\\src\\usbd_audio_if.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\class\\src\\usbd_audio.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\class\\src\\usbd_audio.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\class\\src\\usbd_audio.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\class\\src\\usbd_audio.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usb_device.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usb_device.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usb_device.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usb_device.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_conf.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_conf.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_conf.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_conf.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_core.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_core.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_core.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_core.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_ctlreq.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_ctlreq.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_ctlreq.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_ctlreq.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_desc.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_desc.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_desc.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_desc.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DARM_MATH_CM7 -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/main/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/atSlave -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/tlv320adc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/DSP/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/class/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/App/drivers/usb/core/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_ioreq.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_ioreq.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\App\\drivers\\usb\\core\\Src\\usbd_ioreq.c", "output": "CMakeFiles\\AudioH7_V18.02.dir\\App\\drivers\\usb\\core\\Src\\usbd_ioreq.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32h7xx.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\system_stm32h7xx.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Core\\Src\\system_stm32h7xx.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Core\\Src\\system_stm32h7xx.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_cortex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_cortex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_cortex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_cortex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_rcc_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_flash_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_gpio.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_gpio.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_gpio.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_gpio.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_hsem.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_hsem.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_hsem.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_hsem.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_dma_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_mdma.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_mdma.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_mdma.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_mdma.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pwr_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2c_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_exti.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_exti.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_exti.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_exti.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_fmac.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_fmac.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_fmac.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_fmac.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_i2s_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_uart_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd_ex.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd_ex.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd_ex.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_hal_pcd_ex.c.obj"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "command": "C:\\ST\\STM32CubeCLT_1.16.0\\GNU-tools-for-STM32\\bin\\arm-none-eabi-gcc.exe -DDEBUG -DSTM32H725xx -DUSE_HAL_DRIVER -DUSE_PWR_DIRECT_SMPS_SUPPLY -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include -IC:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc  -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11 -o cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_ll_usb.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_ll_usb.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\STM32\\H7\\Audio_H7_V18.0\\AudioH7_V18.02\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_ll_usb.c", "output": "cmake\\stm32cubemx\\CMakeFiles\\STM32_Drivers.dir\\__\\__\\Drivers\\STM32H7xx_HAL_Driver\\Src\\stm32h7xx_ll_usb.c.obj"}]