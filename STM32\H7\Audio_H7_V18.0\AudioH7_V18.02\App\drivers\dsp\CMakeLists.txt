#CMakeLists.txt for DSP Library

add_library(dsp INTERFACE)

add_subdirectory(BasicMathFunctions)
target_link_libraries(dsp INTERFACE CMSISDSPBasicMath)

add_subdirectory(FilteringFunctions)
target_compile_definitions(CMSISDSPFiltering PUBLIC ARM_FAST_ALLOW_TABLES)
target_link_libraries(dsp INTERFACE CMSISDSPFiltering)

target_include_directories(dsp INTERFACE "${DSP}/../Include")

target_include_directories(dsp INTERFACE 
    ${CMAKE_CURRENT_LIST_DIR}/Include
)
