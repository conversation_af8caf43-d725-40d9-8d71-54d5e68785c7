{"artifacts": [{"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Core/Src/system_stm32h7xx.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c.obj"}, {"path": "cmake/stm32cubemx/CMakeFiles/STM32_Drivers.dir/./__/__/Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c.obj"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_sources"], "files": ["cmake/stm32cubemx/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 87, "parent": 0}, {"command": 1, "file": 0, "line": 89, "parent": 0}, {"command": 2, "file": 0, "line": 88, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": " -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -mcpu=cortex-m7 -mfpu=fpv5-d16 -mfloat-abi=hard  -Wall -Wextra -Wpedantic -fdata-sections -ffunction-sections -O0 -g3 -std=gnu11"}], "defines": [{"backtrace": 2, "define": "DEBUG"}, {"backtrace": 2, "define": "STM32H725xx"}, {"backtrace": 2, "define": "USE_HAL_DRIVER"}, {"backtrace": 2, "define": "USE_PWR_DIRECT_SMPS_SUPPLY"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Core/Inc"}, {"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc"}, {"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/STM32H7xx_HAL_Driver/Inc/Legacy"}, {"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Device/ST/STM32H7xx/Include"}, {"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Drivers/CMSIS/Include"}, {"backtrace": 2, "path": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/Middlewares/ST/ARM/DSP/Inc"}], "language": "C", "languageStandard": {"backtraces": [1], "standard": "11"}, "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "id": "STM32_Drivers::@768a070a0fe75716b479", "name": "STM32_Drivers", "paths": {"build": "cmake/stm32cubemx", "source": "cmake/stm32cubemx"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "Core/Src/system_stm32h7xx.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_cortex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_rcc_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_flash_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_gpio.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_hsem.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_dma_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_mdma.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pwr_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2c_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_exti.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_fmac.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_i2s_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_uart_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_hal_pcd_ex.c", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "Drivers/STM32H7xx_HAL_Driver/Src/stm32h7xx_ll_usb.c", "sourceGroupIndex": 0}], "type": "OBJECT_LIBRARY"}