#include "common.h"
#include "H5.h"
#include "usart.h"
#include "appEntry.h"
#include <stdlib.h>

at_device_t devices[MAX_DEVICES];

int registerDevice(at_device_t device, uint8_t device_id)
{
    if (device_id >= MAX_DEVICES)
    {
        return -1;
    }

    devices[device_id].huart = device.huart;
    devices[device_id].cmdTable = device.cmdTable;
    devices[device_id].evtTable = device.evtTable;
    
    return 0;
}

/*************************************************************************************************************
***************************************Command Functions******************************************************
**************************************************************************************************************/

int ParseCommand(char *cmd_str, uint8_t size, uint8_t device_id)
{
    uint8_t char_idx = 0;
    uint8_t param_idx = 0;
    int state = 0;
    command_t cmd = {0};
    cmd.device = device_id;

    // Iterate through the buffer
    for (int i = 0; i < size; i++)
    {
        char c = cmd_str[i];

        switch (state)
        {
        case 0: // Initial state, looking for the start character
            if (c == INITIAL_CMD_CHAR)
            {
                state = 1; // Transition to the next state
                cmd.command[char_idx++] = c;
                break;
            } else if (c == INITIAL_DBG_CHAR)
            {
                state = 3;
                break;
            } else {
                break;
            }
        
        case 1: // State 1, looking for the event name
            if (c == MSG_PARAM_SEPERATOR)
            {
                state = 2; // Transition to the next state
                char_idx = 0;
                cmd.num_params++;
                break;
            }
            else if ((c == PARAM_END_CHAR) | (c == '\n'))
            {
                state = 0;
                return dispatchCommand(&cmd);
            } else 
            {
                cmd.command[char_idx] = c;
                char_idx++;
            }
            break;
        case 2: // State 2, looking for the parameter
        if (c == PARAM_SEPERATOR)
        {
            param_idx++;
            cmd.num_params++;
            char_idx = 0;
        } else if ((c == PARAM_END_CHAR) | (c == '\n'))
        {
            state = 0;
            return dispatchCommand(&cmd);
        } else {
            cmd.param[param_idx][char_idx] = c;
            char_idx++;
        }
        break;
        case 3: // DBG Message 
        uint8_t dbg_idx = 0;
        dbg_idx += (i + 2);
        uint8_t dbg_len = size - dbg_idx;
        HAL_UART_Transmit(&DBG_UART, ((uint8_t *)cmd_str + dbg_idx), dbg_len, 100);
        return 0;
        break;

        default:
            break;
        }
    }
    return 1; // Return 1 if command not found
}

int dispatchCommand(command_t *cmd)
{
    if (!cmd || !cmd->command)
    {
        return 1; // Return 1 if command is NULL
    }

    for (int i = 0; devices[cmd->device].cmdTable[i].pattern != NULL; i++)
    {
        if (strcmp(cmd->command, devices[cmd->device].cmdTable[i].pattern) == 0)
        {
            switch (devices[cmd->device].cmdTable[i].param_type)
            {
            case PARAM_TYPE_UINT8:
                uint8_t numeric_params[MAX_PARAMS_PER_EVENT] = {0};
                for (int j = 0; j < cmd->num_params; j++)
                {
                    numeric_params[j] = atoi(cmd->param[j]);
                }
                devices[cmd->device].cmdTable[i].handler.u8(numeric_params);
                return 0;
                break;
            case PARAM_TYPE_FLOAT:
                if (devices[cmd->device].cmdTable[i].handler.flt)
                {
                    float numeric_param = atof(cmd->param[0]);
                    devices[cmd->device].cmdTable[i].handler.flt(numeric_param);
                    return 0;
                }
                break;
            case PARAM_TYPE_CHAR:
                if (devices[cmd->device].cmdTable[i].handler.chr)
                {
                    devices[cmd->device].cmdTable[i].handler.chr(cmd->param);
                    return 0;
                }
                break;
            case PARAM_TYPE_SIMPLE:
                if (devices[cmd->device].cmdTable[i].handler.simp)
                {
                    devices[cmd->device].cmdTable[i].handler.simp(0);
                    return 0;
                }
                break;
            default:
                break;
            }
        }
    }
    return 1; // Return 1 if command not found
}







/*************************************************************************************************************
***************************************Event Functions******************************************************
**************************************************************************************************************/

int SendEvent(Event_t *event, uint8_t device_id)
{
    char evt_str[EVT_BUFFER_SIZE] = {0};
    char param_str[MAX_PARAM_LENGTH] = {0};
    uint8_t param_len = 0;
    uint8_t evt_len = 0;
    uint8_t total_len = 0;
    event_pattern_t event_pattern = devices[device_id].evtTable[event->index];

    evt_len = strlen(event_pattern.event);

    if (event->num_params > 0)
    {
        param_str[0] = MSG_PARAM_SEPERATOR;
        param_len = 1;

        for (int i = 0; i < event->num_params; i++)
        {
            param_len += sprintf(&param_str[param_len], "%d", event->param[i]);

            // Add a comma only if there are multiple parameters and this is not the last one
            if (event->num_params > 1 && i < event->num_params - 1)
            {
                param_str[param_len] = PARAM_SEPERATOR;
                param_len++;
            }
        }
        total_len += param_len;
    } 

    memcpy(evt_str, event_pattern.event, evt_len);

    for (int i = 0; i < event->num_params; i++)
    {
        evt_str[evt_len] = param_str[i];
        evt_len++;
    }

    evt_str[evt_len] = '\r';
    evt_len++;
    evt_str[evt_len] = '\n';
    evt_len++;

    // Send the event string
    if (HAL_UART_Transmit(devices[device_id].huart, (uint8_t *)evt_str, evt_len, HAL_MAX_DELAY) != HAL_OK)
    {
        return 1;
    }

    return 0;
}

int Send0ParamEvent(uint8_t evt_idx, uint8_t device_id)
{
    Event_t event;
    event.index = evt_idx;
    event.num_params = 0;
    return SendEvent(&event, device_id);
}


int Send1ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param)
{
    Event_t event;
    event.index = evt_idx;
    event.num_params = 1;
    event.param[0] = param;
    return SendEvent(&event, device_id);
}

int Send2ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param1, uint8_t param2)
{
    Event_t event;
    event.index = evt_idx;
    event.num_params = 2;
    event.param[0] = param1;
    event.param[1] = param2;
    return SendEvent(&event, device_id);
}

int Send3ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param1, uint8_t param2, uint8_t param3)
{
    Event_t event;
    event.index = evt_idx;
    event.num_params = 3;
    event.param[0] = param1;
    event.param[1] = param2;
    event.param[2] = param3;
    return SendEvent(&event, device_id);
}

int Send4ParamEvent(uint8_t evt_idx, uint8_t device_id, uint8_t param1, uint8_t param2, uint8_t param3, uint8_t param4)
{
    Event_t event;
    event.index = evt_idx;
    event.num_params = 4;
    event.param[0] = param1;
    event.param[1] = param2;
    event.param[2] = param3;
    event.param[3] = param4;
    return SendEvent(&event, device_id);
}
