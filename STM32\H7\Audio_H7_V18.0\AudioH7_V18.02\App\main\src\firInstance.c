#include "firInstance.h"
#include "usbAudio.h"
#include "audio.h"
#include "appEntry.h"
#include "usb_device.h"
#include "usbd_audio.h"
#include "usbd_audio_if.h"
#include "filterCoeffs.h"
#include "H5.h"
#include <stdbool.h>

extern float outputVolume;

static inline int16_t floatToInt16(float x)
{
    if (x >  1.0f) x =  1.0f;
    if (x < -1.0f) x = -1.0f;
    return (int16_t)(FLOAT_TO_INT16 * x);
}

static inline float int16ToFloat(int16_t x)
{
    float f = (float)x * INT16_TO_FLOAT;
    if (f >  1.0f) f =  1.0f;
    if (f < -1.0f) f = -1.0f;
    return f;
}

void FirInit(FirContainer_t *fir, const float *coeffs, uint16_t numTaps, float *stateBuf, float *outBuffer)
{
    fir->kernel = coeffs;
    fir->firBuf = stateBuf;
    fir->out = outBuffer;
    fir->numTaps = numTaps;

    arm_fir_init_f32(&(fir->firInstance), numTaps, (float32_t *)coeffs, (float32_t *)fir->firBuf, FIR_BLOCK_LENGTH);
}

void FirUpdate(FirContainer_t *c, float *inp)
{
    arm_fir_f32(&(c->firInstance), inp, c->out, FIR_BLOCK_LENGTH);
}

void FirSetVolume(float volume)
{
    outputVolume = volume;
}