#ifndef TLV320ADC_REG_H
#define TLV320ADC_REG_H

#include <stdint.h>

#include <stdint.h>

// Compact register definition structure - uses 16 bits per register
typedef struct {
    uint8_t addr;       // Register address
    uint8_t page;     // Page number (0-3)
    uint8_t value;    // Default value
    uint8_t mask;     // Mask for reading/writing
} tlv320adc_reg_def_t;

// Register groups for better organization
typedef enum {
    REG_GROUP_SYSTEM,
    REG_GROUP_AUDIO_INTERFACE,
    REG_GROUP_GPIO,
    REG_GROUP_INTERRUPT,
    REG_GROUP_CHANNEL_CONFIG,
    REG_GROUP_DSP,
    REG_GROUP_POWER,
    REG_GROUP_COUNT
} tlv320adc_reg_group_t;

// Register identifiers
typedef enum {
    // System registers
    REG_PAGE_SEL = 0,
    REG_SW_RESET,
    REG_SLEEP_CFG,
    REG_SHDN_CFG,
    R<PERSON>_CLK_SRC,
    REG_PDMCLK_CFG,
    REG_PDMIN_CFG,
    REG_CM_TOL_CFG,
    REG_BIAS_CFG,
    REG_I2C_CKSUM,
    
    // Audio interface registers
    REG_ASI_CFG0,
    REG_ASI_CFG1,
    REG_ASI_CFG2,
    REG_ASI_MIX_CFG,
    REG_ASI_CH1,
    REG_ASI_CH2,
    REG_ASI_CH3,
    REG_ASI_CH4,
    REG_MST_CFG0,
    REG_MST_CFG1,
    REG_ASI_STS,
    REG_ASI_OUT_CH_EN,
    
    // GPIO registers
    REG_GPIO_CFG0,
    REG_GPO_CFG0,
    REG_GPO_VAL,
    REG_GPIO_MON,
    REG_GPI_CFG0,
    REG_GPI_MON,
    
    // Interrupt registers
    REG_INT_CFG,
    REG_INT_MASK0,
    REG_INT_LTCH0,
    
    // Channel configuration registers
    REG_CH1_CFG0,
    REG_CH1_CFG1,
    REG_CH1_CFG2,
    REG_CH1_CFG3,
    REG_CH1_CFG4,
    REG_CH2_CFG0,
    REG_CH2_CFG1,
    REG_CH2_CFG2,
    REG_CH2_CFG3,
    REG_CH2_CFG4,
    REG_CH3_CFG2,
    REG_CH3_CFG3,
    REG_CH3_CFG4,
    REG_CH4_CFG2,
    REG_CH4_CFG3,
    REG_CH4_CFG4,
    
    // DSP registers
    REG_DSP_CFG0,
    REG_DSP_CFG1,
    REG_AGC_CFG0,
    REG_GAIN_CFG,
    
    // Power registers
    REG_IN_CH_EN,
    REG_PWR_CFG,
    REG_DEV_STS0,
    REG_DEV_STS1,

    // Reserved
    REG_COUNT
} tlv320adc_reg_id_t;

typedef enum {
    REG_BQ1_BASE,
    REG_BQ2_BASE,
    REG_BQ3_BASE,
    REG_BQ4_BASE,
    REG_BQ5_BASE,
    REG_BQ6_BASE,
    REG_BQ7_BASE,
    REG_BQ8_BASE,
    REG_BQ9_BASE,
    REG_BQ10_BASE,
    REG_BQ11_BASE,
    REG_BQ12_BASE,
    REG_BQ13_BASE,
    REG_BQ14_BASE,
    FLTR_REG_COUNT
} tlv_filter_base_reg_t;

// Group index table - stores start and count for each group
typedef struct {
    uint8_t start_idx;
    uint8_t count;
} tlv320adc_group_index_t;

// Declare the register definition table and group index table as extern
extern const tlv320adc_reg_def_t tlv_reg[REG_COUNT];
extern const uint8_t tlv_filter_base[FLTR_REG_COUNT];
extern const tlv320adc_group_index_t tlv320adc_groups[REG_GROUP_COUNT];

// Bit definitions for commonly used registers
/* SW_RESET (0x01) */
#define TLV320ADC_SW_RESET_BIT

#endif // TLV320ADC_REG_H