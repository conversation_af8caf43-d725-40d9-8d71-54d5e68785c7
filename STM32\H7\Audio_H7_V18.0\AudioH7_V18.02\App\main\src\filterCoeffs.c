#include "filterCoeffs.h"

// heart_filter: 286 taps
__attribute__((section(".dtcmram")))
const float heart_filter[286] = {
    0.00005106f,     0.00001560f,     0.00001794f,     0.00002051f,     0.00002331f,
    0.00002636f,     0.00002966f,     0.00003324f,     0.00003708f,     0.00004122f,
    0.00004568f,     0.00005048f,     0.00005561f,     0.00006107f,     0.00006688f,
    0.00007313f,     0.00007977f,     0.00008677f,     0.00009427f,     0.00010217f,
    0.00011054f,     0.00011938f,     0.00012872f,     0.00013856f,     0.00014892f,
    0.00015981f,     0.00017125f,     0.00018327f,     0.00019587f,     0.00020906f,
    0.00022286f,     0.00023729f,     0.00025235f,     0.00026806f,     0.00028444f,
    0.00030149f,     0.00031923f,     0.00033767f,     0.00035682f,     0.00037670f,
    0.00039731f,     0.00041866f,     0.00044076f,     0.00046363f,     0.00048726f,
    0.00051166f,     0.00053684f,     0.00056281f,     0.00058956f,     0.00061711f,
    0.00064545f,     0.00067459f,     0.00070453f,     0.00073527f,     0.00076681f,
    0.00079913f,     0.00083226f,     0.00086617f,     0.00090086f,     0.00093634f,
    0.00097258f,     0.00100958f,     0.00104734f,     0.00108583f,     0.00112505f,
    0.00116500f,     0.00120564f,     0.00124697f,     0.00128897f,     0.00133162f,
    0.00137491f,     0.00141882f,     0.00146332f,     0.00150839f,     0.00155401f,
    0.00160016f,     0.00164681f,     0.00169393f,     0.00174150f,     0.00178948f,
    0.00183785f,     0.00188658f,     0.00193564f,     0.00198499f,     0.00203460f,
    0.00208444f,     0.00213447f,     0.00218466f,     0.00223497f,     0.00228536f,
    0.00233580f,     0.00238625f,     0.00243666f,     0.00248700f,     0.00253723f,
    0.00258731f,     0.00263719f,     0.00268684f,     0.00273621f,     0.00278527f,
    0.00283396f,     0.00288225f,     0.00293010f,     0.00297746f,     0.00302430f,
    0.00307056f,     0.00311622f,     0.00316122f,     0.00320552f,     0.00324909f,
    0.00329189f,     0.00333386f,     0.00337498f,     0.00341520f,     0.00345450f,
    0.00349281f,     0.00353012f,     0.00356638f,     0.00360157f,     0.00363563f,
    0.00366855f,     0.00370028f,     0.00373081f,     0.00376008f,     0.00378809f,
    0.00381479f,     0.00384016f,     0.00386418f,     0.00388681f,     0.00390805f,
    0.00392786f,     0.00394623f,     0.00396314f,     0.00397856f,     0.00399249f,
    0.00400491f,     0.00401580f,     0.00402516f,     0.00403297f,     0.00403924f,
    0.00404394f,     0.00404707f,     0.00404864f,     0.00404864f,     0.00404707f,
    0.00404394f,     0.00403924f,     0.00403297f,     0.00402516f,     0.00401580f,
    0.00400491f,     0.00399249f,     0.00397856f,     0.00396314f,     0.00394623f,
    0.00392786f,     0.00390805f,     0.00388681f,     0.00386418f,     0.00384016f,
    0.00381479f,     0.00378809f,     0.00376008f,     0.00373081f,     0.00370028f,
    0.00366855f,     0.00363563f,     0.00360157f,     0.00356638f,     0.00353012f,
    0.00349281f,     0.00345450f,     0.00341520f,     0.00337498f,     0.00333386f,
    0.00329189f,     0.00324909f,     0.00320552f,     0.00316122f,     0.00311622f,
    0.00307056f,     0.00302430f,     0.00297746f,     0.00293010f,     0.00288225f,
    0.00283396f,     0.00278527f,     0.00273621f,     0.00268684f,     0.00263719f,
    0.00258731f,     0.00253723f,     0.00248700f,     0.00243666f,     0.00238625f,
    0.00233580f,     0.00228536f,     0.00223497f,     0.00218466f,     0.00213447f,
    0.00208444f,     0.00203460f,     0.00198499f,     0.00193564f,     0.00188658f,
    0.00183785f,     0.00178948f,     0.00174150f,     0.00169393f,     0.00164681f,
    0.00160016f,     0.00155401f,     0.00150839f,     0.00146332f,     0.00141882f,
    0.00137491f,     0.00133162f,     0.00128897f,     0.00124697f,     0.00120564f,
    0.00116500f,     0.00112505f,     0.00108583f,     0.00104734f,     0.00100958f,
    0.00097258f,     0.00093634f,     0.00090086f,     0.00086617f,     0.00083226f,
    0.00079913f,     0.00076681f,     0.00073527f,     0.00070453f,     0.00067459f,
    0.00064545f,     0.00061711f,     0.00058956f,     0.00056281f,     0.00053684f,
    0.00051166f,     0.00048726f,     0.00046363f,     0.00044076f,     0.00041866f,
    0.00039731f,     0.00037670f,     0.00035682f,     0.00033767f,     0.00031923f,
    0.00030149f,     0.00028444f,     0.00026806f,     0.00025235f,     0.00023729f,
    0.00022286f,     0.00020906f,     0.00019587f,     0.00018327f,     0.00017125f,
    0.00015981f,     0.00014892f,     0.00013856f,     0.00012872f,     0.00011938f,
    0.00011054f,     0.00010217f,     0.00009427f,     0.00008677f,     0.00007977f,
    0.00007313f,     0.00006688f,     0.00006107f,     0.00005561f,     0.00005048f,
    0.00004568f,     0.00004122f,     0.00003708f,     0.00003324f,     0.00002966f,
    0.00002636f,     0.00002331f,     0.00002051f,     0.00001794f,     0.00001560f,
    0.00005106f
};

// lung_filter: 351 taps
__attribute__((section(".dtcmram")))
const float lung_filter[351] = {
    0.01837280f,     -0.00059255f,     -0.00058701f,     -0.00058389f,     -0.00058272f,
    -0.00058298f,     -0.00058404f,     -0.00058524f,     -0.00058582f,     -0.00058497f,
    -0.00058205f,     -0.00057635f,     -0.00056736f,     -0.00055435f,     -0.00053787f,
    -0.00051674f,     -0.00049184f,     -0.00046335f,     -0.00043161f,     -0.00039725f,
    -0.00036137f,     -0.00032523f,     -0.00029027f,     -0.00025798f,     -0.00022982f,
    -0.00020741f,     -0.00019274f,     -0.00018823f,     -0.00019441f,     -0.00021413f,
    -0.00024834f,     -0.00029850f,     -0.00036571f,     -0.00045085f,     -0.00055437f,
    -0.00067643f,     -0.00081683f,     -0.00097487f,     -0.00114944f,     -0.00133898f,
    -0.00155333f,     -0.00173919f,     -0.00197056f,     -0.00220232f,     -0.00243265f,
    -0.00265898f,     -0.00287834f,     -0.00308738f,     -0.00328264f,     -0.00346054f,
    -0.00361748f,     -0.00375013f,     -0.00385542f,     -0.00393077f,     -0.00397338f,
    -0.00398255f,     -0.00395634f,     -0.00389470f,     -0.00379804f,     -0.00366730f,
    -0.00350404f,     -0.00331068f,     -0.00309048f,     -0.00284740f,     -0.00258607f,
    -0.00231143f,     -0.00202864f,     -0.00174327f,     -0.00146289f,     -0.00119210f,
    -0.00093830f,     -0.00070737f,     -0.00050531f,     -0.00033774f,     -0.00020983f,
    -0.00012610f,     -0.00009036f,     -0.00010565f,     -0.00017404f,     -0.00029661f,
    -0.00047170f,     -0.00070751f,     -0.00098267f,     -0.00131035f,     -0.00168303f,
    -0.00209390f,     -0.00253605f,     -0.00300223f,     -0.00348464f,     -0.00397498f,
    -0.00446456f,     -0.00494418f,     -0.00540461f,     -0.00583656f,     -0.00623140f,
    -0.00657974f,     -0.00687462f,     -0.00710840f,     -0.00727511f,     -0.00736995f,
    -0.00738943f,     -0.00733120f,     -0.00719443f,     -0.00697989f,     -0.00669004f,
    -0.00632922f,     -0.00590333f,     -0.00541939f,     -0.00488463f,     -0.00431131f,
    -0.00370870f,     -0.00308962f,     -0.00246680f,     -0.00185362f,     -0.00126374f,
    -0.00071091f,     -0.00020865f,     0.00023005f,     0.00059290f,     0.00086866f,
    0.00104700f,     0.00112229f,     0.00108059f,     0.00092618f,     0.00065268f,
    0.00025903f,     -0.00025256f,     -0.00087722f,     -0.00160779f,     -0.00243480f,
    -0.00334665f,     -0.00432968f,     -0.00536808f,     -0.00644417f,     -0.00753850f,
    -0.00863101f,     -0.00969909f,     -0.01072104f,     -0.01167390f,     -0.01253496f,
    -0.01328202f,     -0.01389386f,     -0.01435032f,     -0.01463267f,     -0.01472403f,
    -0.01460969f,     -0.01427776f,     -0.01371968f,     -0.01293036f,     -0.01190392f,
    -0.01064397f,     -0.00915296f,     -0.00743880f,     -0.00551243f,     -0.00338821f,
    -0.00108368f,     0.00138053f,     0.00398099f,     0.00669182f,     0.00948490f,
    0.01233041f,     0.01519602f,     0.01805269f,     0.02086246f,     0.02359618f,
    0.02622131f,     0.02870579f,     0.03101917f,     0.03313315f,     0.03502178f,
    0.03666177f,     0.03803281f,     0.03911761f,     0.03990243f,     0.04037727f,
    0.04053651f,     0.04037727f,     0.03990243f,     0.03911761f,     0.03803281f,
    0.03666177f,     0.03502178f,     0.03313315f,     0.03101917f,     0.02870579f,
    0.02622131f,     0.02359618f,     0.02086246f,     0.01805269f,     0.01519602f,
    0.01233041f,     0.00948490f,     0.00669182f,     0.00398099f,     0.00138053f,
    -0.00108368f,     -0.00338821f,     -0.00551243f,     -0.00743880f,     -0.00915296f,
    -0.01064397f,     -0.01190392f,     -0.01293036f,     -0.01371968f,     -0.01427776f,
    -0.01460969f,     -0.01472403f,     -0.01463267f,     -0.01435032f,     -0.01389386f,
    -0.01328202f,     -0.01253496f,     -0.01167390f,     -0.01072104f,     -0.00969909f,
    -0.00863101f,     -0.00753850f,     -0.00644417f,     -0.00536808f,     -0.00432968f,
    -0.00334665f,     -0.00243480f,     -0.00160779f,     -0.00087722f,     -0.00025256f,
    0.00025903f,     0.00065268f,     0.00092618f,     0.00108059f,     0.00112229f,
    0.00104700f,     0.00086866f,     0.00059290f,     0.00023005f,     -0.00020865f,
    -0.00071091f,     -0.00126374f,     -0.00185362f,     -0.00246680f,     -0.00308962f,
    -0.00370870f,     -0.00431131f,     -0.00488463f,     -0.00541939f,     -0.00590333f,
    -0.00632922f,     -0.00669004f,     -0.00697989f,     -0.00719443f,     -0.00733120f,
    -0.00738943f,     -0.00736995f,     -0.00727511f,     -0.00710840f,     -0.00687462f,
    -0.00657974f,     -0.00623140f,     -0.00583656f,     -0.00540461f,     -0.00494418f,
    -0.00446456f,     -0.00397498f,     -0.00348464f,     -0.00300223f,     -0.00253605f,
    -0.00209390f,     -0.00168303f,     -0.00131035f,     -0.00098267f,     -0.00070751f,
    -0.00047170f,     -0.00029661f,     -0.00017404f,     -0.00010565f,     -0.00009036f,
    -0.00012610f,     -0.00020983f,     -0.00033774f,     -0.00050531f,     -0.00070737f,
    -0.00093830f,     -0.00119210f,     -0.00146289f,     -0.00174327f,     -0.00202864f,
    -0.00231143f,     -0.00258607f,     -0.00284740f,     -0.00309048f,     -0.00331068f,
    -0.00350404f,     -0.00366730f,     -0.00379804f,     -0.00389470f,     -0.00395634f,
    -0.00398255f,     -0.00397338f,     -0.00393077f,     -0.00385542f,     -0.00375013f,
    -0.00361748f,     -0.00346054f,     -0.00328264f,     -0.00308738f,     -0.00287834f,
    -0.00265898f,     -0.00243265f,     -0.00220232f,     -0.00197056f,     -0.00173919f,
    -0.00155333f,     -0.00133898f,     -0.00114944f,     -0.00097487f,     -0.00081683f,
    -0.00067643f,     -0.00055437f,     -0.00045085f,     -0.00036571f,     -0.00029850f,
    -0.00024834f,     -0.00021413f,     -0.00019441f,     -0.00018823f,     -0.00019274f,
    -0.00020741f,     -0.00022982f,     -0.00025798f,     -0.00029027f,     -0.00032523f,
    -0.00036137f,     -0.00039725f,     -0.00043161f,     -0.00046335f,     -0.00049184f,
    -0.00051674f,     -0.00053787f,     -0.00055435f,     -0.00056736f,     -0.00057635f,
    -0.00058205f,     -0.00058497f,     -0.00058582f,     -0.00058524f,     -0.00058404f,
    -0.00058298f,     -0.00058272f,     -0.00058389f,     -0.00058701f,     -0.00059255f,
    0.01837280f
};

// balanced_filter: 179 taps
__attribute__((section(".dtcmram"))) 
const float balanced_filter[179] = {
    -0.00005674f,     -0.00004293f,     -0.00005869f,     -0.00007785f,     -0.00010081f,
    -0.00012798f,     -0.00015975f,     -0.00019654f,     -0.00023871f,     -0.00028663f,
    -0.00034061f,     -0.00040092f,     -0.00046778f,     -0.00054135f,     -0.00062171f,
    -0.00070884f,     -0.00080263f,     -0.00090286f,     -0.00100921f,     -0.00112120f,
    -0.00123821f,     -0.00135947f,     -0.00148406f,     -0.00161093f,     -0.00173881f,
    -0.00186627f,     -0.00199170f,     -0.00211337f,     -0.00222932f,     -0.00233741f,
    -0.00243540f,     -0.00252089f,     -0.00259123f,     -0.00264385f,     -0.00267586f,
    -0.00268440f,     -0.00266651f,     -0.00261917f,     -0.00253934f,     -0.00242400f,
    -0.00227013f,     -0.00207479f,     -0.00183515f,     -0.00154847f,     -0.00121219f,
    -0.00082393f,     -0.00038153f,     0.00011691f,     0.00067299f,     0.00128804f,
    0.00196300f,     0.00269845f,     0.00349457f,     0.00435116f,     0.00526754f,
    0.00624259f,     0.00727475f,     0.00836195f,     0.00950169f,     0.01069096f,
    0.01192633f,     0.01320384f,     0.01451915f,     0.01586747f,     0.01724360f,
    0.01864199f,     0.02005670f,     0.02148154f,     0.02291001f,     0.02433540f,
    0.02575082f,     0.02714924f,     0.02852357f,     0.02986667f,     0.03117143f,
    0.03243082f,     0.03363796f,     0.03478615f,     0.03586893f,     0.03688013f,
    0.03781394f,     0.03866494f,     0.03942815f,     0.04009906f,     0.04067372f,
    0.04114866f,     0.04152106f,     0.04178868f,     0.04194990f,     0.04200375f,
    0.04194990f,     0.04178868f,     0.04152106f,     0.04114866f,     0.04067372f,
    0.04009906f,     0.03942815f,     0.03866494f,     0.03781394f,     0.03688013f,
    0.03586893f,     0.03478615f,     0.03363796f,     0.03243082f,     0.03117143f,
    0.02986667f,     0.02852357f,     0.02714924f,     0.02575082f,     0.02433540f,
    0.02291001f,     0.02148154f,     0.02005670f,     0.01864199f,     0.01724360f,
    0.01586747f,     0.01451915f,     0.01320384f,     0.01192633f,     0.01069096f,
    0.00950169f,     0.00836195f,     0.00727475f,     0.00624259f,     0.00526754f,
    0.00435116f,     0.00349457f,     0.00269845f,     0.00196300f,     0.00128804f,
    0.00067299f,     0.00011691f,     -0.00038153f,     -0.00082393f,     -0.00121219f,
    -0.00154847f,     -0.00183515f,     -0.00207479f,     -0.00227013f,     -0.00242400f,
    -0.00253934f,     -0.00261917f,     -0.00266651f,     -0.00268440f,     -0.00267586f,
    -0.00264385f,     -0.00259123f,     -0.00252089f,     -0.00243540f,     -0.00233741f,
    -0.00222932f,     -0.00211337f,     -0.00199170f,     -0.00186627f,     -0.00173881f,
    -0.00161093f,     -0.00148406f,     -0.00135947f,     -0.00123821f,     -0.00112120f,
    -0.00100921f,     -0.00090286f,     -0.00080263f,     -0.00070884f,     -0.00062171f,
    -0.00054135f,     -0.00046778f,     -0.00040092f,     -0.00034061f,     -0.00028663f,
    -0.00023871f,     -0.00019654f,     -0.00015975f,     -0.00012798f,     -0.00010081f,
    -0.00007785f,     -0.00005869f,     -0.00004293f,     -0.00005674f
};