#include "usbAudio.h"
#include "audio.h"
#include "appEntry.h"
#include "usb_device.h"
#include "usbd_audio.h"
#include "usbd_audio_if.h"
#include "filterCoeffs.h"
#include "H5.h"
#include <stdbool.h>
#include "arm_math.h"  // For CMSIS DSP functions

// Cycle counting macros for performance measurement
#define DWT_CTRL    (*(volatile uint32_t*)0xE0001000)
#define DWT_CYCCNT  (*(volatile uint32_t*)0xE0001004)
#define DEM_CR      (*(volatile uint32_t*)0xE000EDFC)
#define DEM_CR_TRCENA (1 << 24)

// Initialize cycle counter
static inline void cycle_counter_init(void) {
    DEM_CR |= DEM_CR_TRCENA;
    DWT_CYCCNT = 0;
    DWT_CTRL |= 1;
}

// Start cycle measurement
static inline uint32_t cycle_counter_start(void) {
    return DWT_CYCCNT;
}

// Get elapsed cycles
static inline uint32_t cycle_counter_get_elapsed(uint32_t start) {
    return DWT_CYCCNT - start;
}

// Toggle between conversion methods for testing
// Set to 0 for current method, 1 for CMSIS method
#define USE_CMSIS_CONVERSION 0

__attribute__((section(".RAM_D2")))
__attribute__((aligned(32))) int16_t audio_in[AUDIO_BUFFER_SIZE];

__attribute__((section(".RAM_D2")))
__attribute__((aligned(32))) int16_t audio_out[AUDIO_BUFFER_SIZE];

__attribute__((section(".RAM_D2")))
__attribute__((aligned(32))) int16_t usb_audio_out[MIC_SAMPLES_PER_PACKET];

__attribute__((aligned(32))) static int16_t audio_in_left[FIR_BLOCK_LENGTH];
__attribute__((aligned(32))) static float processBuffer[FIR_BLOCK_LENGTH];
__attribute__((aligned(32))) static float firOutputBuffer[FIR_BLOCK_LENGTH];

volatile int16_t *_sampleBuffer; // 16-bit left channel samples
int16_t *_fillBuffer;
int16_t *_sendBuffer; // Final buffer sent to USB
int16_t *_usbBuffer;

uint8_t audioRunning = 0;
uint8_t audioDataReady = 0;
uint8_t audioProcessingFlag = 0;
uint8_t txDMAStarted = 0;
uint8_t AckReceived = 0;
extern USBD_HandleTypeDef hUsbDeviceHS;

FirContainer_t fir;
float outputVolume = 3.0f;
static float BALANCED_firStateBuf[BALANCED_KERNAL_LENGTH + FIR_BLOCK_LENGTH - 1];
static float HEART_firStateBuf[HEART_KERNAL_LENGTH + FIR_BLOCK_LENGTH - 1];
static float LUNG_firStateBuf[LUNG_KERNAL_LENGTH + FIR_BLOCK_LENGTH - 1];

int audioSourceInit(AudioSource_t *audio)
{
    int ret = 0;
    HAL_GPIO_WritePin(EN_ADC_GPIO_Port, EN_ADC_Pin, GPIO_PIN_SET);
    HAL_Delay(300);
    ret = tlvInit(audio->I2C, audio->filter);
    if (ret != 0)
    {
        printf("Error Initializing TLV320\r\n");
        Send2ParamEvent(EV_ADC_ERROR, H5, ADC_ERROR_AUDIO_INIT_FAILED, ADC_ERROR_SUBCODE_TLV_INIT);
    }
    return ret;
}

int audioStart(AudioSource_t *audio)
{
    HAL_StatusTypeDef status;
    _sampleBuffer = (volatile int16_t *)audio_in;
    _fillBuffer = (int16_t *)audio_out;
    _usbBuffer = (int16_t *)usb_audio_out;
    _sendBuffer = (int16_t *)&audio_out[AUDIO_BUFFER_SIZE / 2];
    float *filter;
    float *firStateBuf;
    uint32_t kernalLength = 0;

    memset(audio_in, 0, AUDIO_BUFFER_SIZE * sizeof(int16_t));
    memset(audio_out, 0, AUDIO_BUFFER_SIZE * sizeof(int16_t));
    memset(usb_audio_out, 0, MIC_SAMPLES_PER_PACKET * sizeof(int16_t));
    memset(firOutputBuffer, 0, FIR_BLOCK_LENGTH * sizeof(float));

    // Initialize cycle counter for performance measurement
    cycle_counter_init();

    switch (audio->filter)
    {
    case BALANCED_MODE:
        filter = balanced_filter;
        kernalLength = BALANCED_KERNAL_LENGTH;
        firStateBuf = BALANCED_firStateBuf;
        FirInit(&fir, filter, kernalLength, firStateBuf, firOutputBuffer);
        break;
    case LUNG_MODE:
        filter = lung_filter;
        kernalLength = LUNG_KERNAL_LENGTH;
        firStateBuf = LUNG_firStateBuf;
        FirInit(&fir, filter, kernalLength, firStateBuf, firOutputBuffer);
        break;
    case HEART_MODE:
        filter = heart_filter;
        kernalLength = HEART_KERNAL_LENGTH;
        firStateBuf = HEART_firStateBuf;
        FirInit(&fir, filter, kernalLength, firStateBuf, firOutputBuffer);
        break;
    case NO_FILTER:
        break;
    default:
        printf("Invalid filter selected\r\n");
        return audioStateError;
    }

    if (audio->mode != modeUSB)
    {
        SCB_InvalidateDCache_by_Addr((uint32_t *)(((uint32_t)audio_in) & ~(uint32_t)0x1F), (AUDIO_BUFFER_SIZE + 32));
        __DSB();
        SCB_InvalidateDCache_by_Addr((uint32_t *)(((uint32_t)audio_out) & ~(uint32_t)0x1F), (AUDIO_BUFFER_SIZE + 32));
        __DSB();

        status = HAL_I2S_Receive_DMA(audio->InI2S, (uint16_t *)audio_in, AUDIO_BUFFER_SIZE);
        if (status != HAL_OK)
        {
            printf("Error Starting I2S receive DMA\r\n");
            updateAudioState(AUDIO_STATE_ERROR, 0);
            Send2ParamEvent(EV_ADC_ERROR, H5, ADC_ERROR_AUDIO_START_FAILED, ADC_ERROR_SUBCODE_I2S_RECEIVE);
            return audioStateError;
        }

        /* status = HAL_I2S_Transmit_DMA(audio->OutI2S, (uint16_t *)_sendBuffer, AUDIO_BUFFER_SIZE);
        if (status != HAL_OK)
        {
            printf("Error Starting I2S transmit DMA\r\n");
            Send2ParamEvent(EV_H5_ERROR, H5, ADC_ERROR_AUDIO_START_FAILED, ADC_ERROR_SUBCODE_I2S_TRANSMIT);
            return audioStateError;
        } */
    }
    else if (audio->mode == modeUSB)
    {
        MX_USB_DEVICE_Init();
        txDMAStarted = 1;
    }

    AckReceived = 0;
    int count = 0;
    Send0ParamEvent(EV_ADC_READY, H5);
    while (AckReceived == 0)
    {
        if (count++ < 6)
        {
            HAL_Delay(1000);
            Send0ParamEvent(EV_ADC_READY, H5);
        }
        else
        {
            Send2ParamEvent(EV_ADC_ERROR, H5, ADC_ERROR_ACK_NOT_RECEIVED, ADC_ERROR_SUBCODE_NONE);
            break;
        }
    }
    audioRunning = 1;
    audioRun(audio);
}

void audioStartTx(void)
{
    HAL_StatusTypeDef status;
    AudioSource_t *audio = getAudioSource();
    status = HAL_I2S_Transmit_DMA(audio->OutI2S, (uint16_t *)audio_out, AUDIO_BUFFER_SIZE);
    if (status != HAL_OK)
    {
        printf("Error Starting I2S transmit DMA\r\n");
        updateAudioState(AUDIO_STATE_ERROR, 0);
        Send2ParamEvent(EV_ADC_ERROR, H5, ADC_ERROR_AUDIO_START_FAILED, ADC_ERROR_SUBCODE_I2S_TRANSMIT);
        uint32_t error = HAL_I2S_GetError(audio->OutI2S);
    } else {
        updateAudioState(AUDIO_STATE_STREAMING, 1);
        txDMAStarted = 1;
    }
}

void audioRun(AudioSource_t *audio)
{
    UNUSED(audio);
    while (audioRunning)
    {
        if (audioDataReady)
        {
            audioProcess();
        }
    }
}

static inline int16_t floatToInt16(float x)
{
    if (x > 1.0f)
        x = 1.0f;
    if (x < -1.0f)
        x = -1.0f;
    return (int16_t)(FLOAT_TO_INT16 * x);
}

static inline float int16ToFloat(int16_t x)
{
    float f = (float)x * INT16_TO_FLOAT;
    if (f > 1.0f)
        f = 1.0f;
    if (f < -1.0f)
        f = -1.0f;
    return f;
}

void audioProcess(void)
{
    float leftIn;
    audioDataReady = 0;
    audioProcessingFlag = 1;
    static uint8_t audioTransferFlag = 0;
    static uint32_t bufferEmpty = 0;
    uint8_t emptycount = 0;
    AudioSource_t *audio = getAudioSource();
    int16_t *src16 = _sampleBuffer;

    // Performance measurement variables
    uint32_t start_cycles, conversion_cycles;
    static uint32_t max_cycles = 0, min_cycles = 0xFFFFFFFF, avg_cycles = 0;
    static uint32_t cycle_count = 0;

    // Start measuring conversion cycles
    start_cycles = cycle_counter_start();

#if USE_CMSIS_CONVERSION
    // CMSIS approach: Separate then batch convert
    // Step 1: Separate left channel
    for (uint16_t n = 0; n < FIR_BLOCK_LENGTH; n++)
    {
        if (emptycount < 10)
        {
            if (*src16 == 0)
            {
                emptycount++;
            }
        }
        else if (emptycount == 10)
        {
            emptycount++;
            bufferEmpty++;
        }
        audio_in_left[n] = *src16;  // Just copy, no conversion
        src16 += 2;
    }

    // Step 2: Batch convert using CMSIS
    arm_q15_to_float(audio_in_left, processBuffer, FIR_BLOCK_LENGTH);
#else
    // Current approach: Individual conversion
    for (uint16_t n = 0; n < FIR_BLOCK_LENGTH; n++)
    {
        if (emptycount < 10)
        {
            if (*src16 == 0)
            {
                emptycount++;
            }
        }
        else if (emptycount == 10)
        {
            emptycount++;
            bufferEmpty++;
        }
        leftIn = int16ToFloat(*src16);
        processBuffer[n] = leftIn;
        src16 += 2;
    }
#endif

    // Measure conversion cycles
    conversion_cycles = cycle_counter_get_elapsed(start_cycles);

    // Update statistics
    cycle_count++;
    if (conversion_cycles > max_cycles) max_cycles = conversion_cycles;
    if (conversion_cycles < min_cycles) min_cycles = conversion_cycles;
    avg_cycles = (avg_cycles * (cycle_count - 1) + conversion_cycles) / cycle_count;

    // Print statistics every 1000 cycles (adjust as needed)
    if (cycle_count % 1000 == 0)
    {
#if USE_CMSIS_CONVERSION
        printf("CMSIS Conversion cycles - Min: %lu, Max: %lu, Avg: %lu, Current: %lu\r\n",
               min_cycles, max_cycles, avg_cycles, conversion_cycles);
#else
        printf("Individual Conversion cycles - Min: %lu, Max: %lu, Avg: %lu, Current: %lu\r\n",
               min_cycles, max_cycles, avg_cycles, conversion_cycles);
#endif
    }

    int16_t *dest;
    float *src = &processBuffer[0];

    if (audio->mode != modeUSB)
    {
        dest = _fillBuffer;
    }
    else
    {
        dest = _usbBuffer;
    }

    if (audio->filter != NO_FILTER)
    {
        FirUpdate(&fir, processBuffer);
        arm_scale_f32(firOutputBuffer, outputVolume, processBuffer, FIR_BLOCK_LENGTH);
    }
    else
    {
        arm_scale_f32(processBuffer, outputVolume, processBuffer, FIR_BLOCK_LENGTH);
    }

    if (audio->mode == modeUSB)
    {
        for (uint16_t i = 0; i < FIR_BLOCK_LENGTH; i++)
        {
            int16_t sample = floatToInt16(*src++);
            *dest++ = sample;
        }

        if (USBD_AUDIO_Data_Transfer(&hUsbDeviceHS, _fillBuffer, MIC_SAMPLES_PER_PACKET / 2) != USBD_OK)
        {
            printf("USB Audio Data Transfer Failed\n");
        }
    }
    else
    {
        for (uint16_t i = 0; i < FIR_BLOCK_LENGTH; i++)
        {
            int16_t sample = floatToInt16(*src++);
            *dest++ = sample;
            *dest++ = sample;
        }
    }
    if (bufferEmpty > 100)
    {
        checkTLVStatus();
    }

    audioProcessingFlag = 0;
}

void HAL_I2S_RxHalfCpltCallback(I2S_HandleTypeDef *hi2s)
{
    if (audioProcessingFlag == 0)
    {
        _sampleBuffer = &audio_in[0];
        _fillBuffer = &audio_out[0];
        _usbBuffer = &usb_audio_out[0];
        audioDataReady = 1;
    }
    else
    {
        Send2ParamEvent(EV_ADC_ERROR, H5, ADC_ERROR_AUDIO_PROCESSING, ADC_ERROR_SUBCODE_PROCESS_SLOW);
        printf("Audio still Processing\n");
    }
}

void HAL_I2S_RxCpltCallback(I2S_HandleTypeDef *hi2s)
{
    if (txDMAStarted == 0)
    {
        audioStartTx();
    }
    _sampleBuffer = &audio_in[AUDIO_BUFFER_SIZE / 2];
    _fillBuffer = &audio_out[AUDIO_BUFFER_SIZE / 2];
    _usbBuffer = &usb_audio_out[AUDIO_BUFFER_SIZE / 4];
    audioDataReady = 1;
}

void HAL_I2S_TxHalfCpltCallback(I2S_HandleTypeDef *hi2s)
{
    _sendBuffer = &audio_out[0];
}

void HAL_I2S_TxCpltCallback(I2S_HandleTypeDef *hi2s)
{
    _sendBuffer = &audio_out[AUDIO_BUFFER_SIZE / 2];
}

void HAL_I2S_ErrorCallback(I2S_HandleTypeDef *hi2s)
{
    if (hi2s->ErrorCode & HAL_I2S_ERROR_OVR)
    {
        printf("I2S Overrun Error\n");
    }
    if (hi2s->ErrorCode & HAL_I2S_ERROR_UDR)
    {
        printf("I2S Underrun Error\n");
    }

    if (hi2s->Instance == SPI1)
    {
        printf("I2S1 Error\n");
    }
    else if (hi2s->Instance == SPI2)
    {
        printf("I2S2 Error\n");
    }
    else if (hi2s->Instance == SPI3)
    {
        printf("I2S3 Error\n");
    }
}

void ackReceivedCallback(void)
{
    AckReceived = 1;
}
