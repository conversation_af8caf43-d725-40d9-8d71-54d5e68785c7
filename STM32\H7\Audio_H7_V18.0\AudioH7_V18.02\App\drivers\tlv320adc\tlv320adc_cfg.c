#include "tlv320adc.h"

static tlv320adc_t tlv;

int tlvInit(I2C_HandleTypeDef *hi2c, int filterMode)
{
    static int _initialized = 0;
    int ret = 0;
    int err = 0;
    uint8_t inChEn = 0;
    uint8_t outChEn = 0;

    if (_initialized == 0)
    {
        tlv.i2c = hi2c;
        tlv.addr = TLV320ADC_I2C_ADDR;
        tlvInitFilter();
        _initialized = 1;
        tlv.OutChs = 0;
        tlv.InChs = 0;
    }

    err = tlvReset();
    if (err)
    {
        return TLV_RESET_ERROR;
    }
    ret += err;
    tlvDelay(TLV_RESET_DELAY);

    err = tlvSleep(WAKE);
    if (err)
    {
        return TLV_WAKE_ERROR;
    }
    ret += err;

    err = configureInputChannel(&inChEn);
    if (err)
    {
        return TLV_INPUT_CHANNEL_CFG_ERROR;
    }
    ret += err;

    err = configureOutputChannel(&outChEn);
    if (err)
    {
        return TLV_OUTPUT_CHANNEL_CFG_ERROR;
    }
    ret += err;

    err = configureAudio();
    if (err)
    {
        return TLV_AUDIO_CFG_ERROR;
    }
    ret += err;

    err = configureFilter(filterMode);
    if (err)
    {
        return TLV_FILTER_CFG_ERROR;
    }
    ret += err;

   /*  err = configureGPIO();
    if (err)
    {
        return TLV_GPIO_CFG_ERROR;
    } */

    err = tlvEnableChannels(inChEn, outChEn);
    if (err)
    {
        return TLV_FILTER_CFG_ERROR;
    }
    ret += err;

    err = tlvPowerUp();
    if (err)
    {
        return TLV_POWER_UP_ERROR;
    }
    ret += err;

    return ret;
}

int tlvWrite(uint8_t regID, uint8_t data)
{
    tlvDelay(I2C_WRITE_DELAY);
    static uint8_t current_page = 0;
    uint8_t checkData = 0;

    // Check if we need to change page
    if (tlv_reg[regID].page != current_page)
    {
        uint8_t page_data = tlv_reg[regID].page;

        // Write to page select register (REG_PAGE_SEL)
        if (HAL_I2C_Mem_Write(tlv.i2c, tlv.addr, tlv_reg[REG_PAGE_SEL].addr,
                              I2C_MEMADD_SIZE_8BIT, &page_data, 1, I2C_TIMEOUT) != HAL_OK)
        {
            return 1;
        }

        current_page = page_data;
        tlvDelay(I2C_WRITE_DELAY);
    }

    // Write to the target register
    if (HAL_I2C_Mem_Write(tlv.i2c, tlv.addr, tlv_reg[regID].addr,
                          I2C_MEMADD_SIZE_8BIT, &data, 1, I2C_TIMEOUT) != HAL_OK)
    {
        return 1;
    }

    tlvDelay(I2C_WRITE_DELAY);

    /* if (HAL_I2C_Mem_Read(tlv.i2c, TLV320ADC_I2C_READ_ADDR, tlv_reg[regID].addr, I2C_MEMADD_SIZE_8BIT, &checkData, 1, I2C_TIMEOUT) != HAL_OK)
    {
        return 1;
    }
    printf("reg: %d, data: %d, checkData: %d\r\n", regID, data, checkData); */

    return 0;
}

int tlvWriteFilter(uint8_t reg, uint8_t *data)
{
    tlvDelay(I2C_WRITE_DELAY);
    if (HAL_I2C_Mem_Write(tlv.i2c, tlv.addr, reg, I2C_MEMADD_SIZE_8BIT, data, 1, I2C_TIMEOUT) != HAL_OK)
    {
        return 1;
    }
    return 0;
}

int tlvWriteLong(uint8_t reg, uint8_t *data, uint8_t len)
{
    tlvDelay(I2C_WRITE_DELAY);
    if (HAL_I2C_Mem_Write(tlv.i2c, tlv.addr, reg, I2C_MEMADD_SIZE_8BIT, data, len, I2C_TIMEOUT) != HAL_OK)
    {
        return 1;
    }
    return 0;
}

int tlvRead(uint8_t reg, uint8_t *data)
{
    tlvDelay(I2C_WRITE_DELAY);
    if (HAL_I2C_Mem_Read(tlv.i2c, TLV320ADC_I2C_READ_ADDR, reg, I2C_MEMADD_SIZE_8BIT, data, 1, I2C_TIMEOUT) != HAL_OK)
    {
        return 1;
    }
    return 0;
}

void tlvDBG(const char *format, ...)
{
    printf(format);
}

void tlvDBGE(const char *format, ...)
{
    printf(format);
}

void tlvDelay(uint32_t ms)
{
    HAL_Delay(ms);
}

void setChannels(int direction, int num)
{
    if (direction == OUTPUT)
    {
        tlv.OutChs = num;
    }
    else if (direction == INPUT)
    {
        tlv.InChs = num;
    } else {
        tlvDBG("Invalid CH direction\n");
    }
}

int getChannels(int direction)
{
    if (direction == OUTPUT)
    {
        return tlv.OutChs;
    }
    else
    {
        return tlv.InChs;
    }
}

void checkTLVStatus(void)
{
    tlvStatus_t status;
    int err = tlvGetStatus(&status);
    if (err)
    {
        tlvDBG("Error reading status\n");
    }

    if (status.CH1 == 0)
    {
        tlvDBG("Channel 1 off\n");
    } else if (status.CH1 == 1) 
    {
        tlvDBG("Channel 1 on\n");
    } else {
        tlvDBG("Channel 1 error\n");
    }

    if (status.CH2 == 0)
    {
        tlvDBG("Channel 2 off\n");
    } else if (status.CH2 == 1)
    {
        tlvDBG("Channel 2 on\n");
    } else {
        tlvDBG("Channel 2 error\n");
    }

    if (status.device_mode == TLV_SLEEP)
    {
        tlvDBG("Device in sleep mode\n");
    } else if (status.device_mode == ALL_CH_PWRD_DOWN)
    {
        tlvDBG("All channels powered down\n");
    } else if (status.device_mode == ACTIVE)
    {
        tlvDBG("Device active\n");
    } else {
        tlvDBG("Device error\n");
    }
}