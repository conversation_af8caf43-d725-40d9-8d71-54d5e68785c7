{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/ST/STM32CubeCLT_1.16.0/CMake/bin/cmake.exe", "cpack": "C:/ST/STM32CubeCLT_1.16.0/CMake/bin/cpack.exe", "ctest": "C:/ST/STM32CubeCLT_1.16.0/CMake/bin/ctest.exe", "root": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28"}, "version": {"isDirty": false, "major": 3, "minor": 28, "patch": 1, "string": "3.28.1", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-571e2232749ad131a843.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "cache-v2-39094eeac0833c2c915e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-875c59ac4051f6e42e40.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, {"jsonFile": "toolchains-v1-4cc47e0eb9a6da338806.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-39094eeac0833c2c915e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-571e2232749ad131a843.json", "kind": "codemodel", "version": {"major": 2, "minor": 6}}, {"jsonFile": "toolchains-v1-4cc47e0eb9a6da338806.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-875c59ac4051f6e42e40.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}]}}}}