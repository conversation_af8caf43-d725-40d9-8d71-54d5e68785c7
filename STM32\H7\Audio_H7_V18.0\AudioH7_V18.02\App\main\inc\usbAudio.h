#ifndef USBAUDIO_H
#define USBAUDIO_H

#include "firInstance.h"
#include <stdbool.h>

#define USB_FIR_BLOCK_LENGTH (MIC_SAMPLES_PER_PACKET / 2)

/**
 * @brief Initialize USB audio system with specified audio source and filter
 * @param audio Pointer to AudioSource_t structure containing configuration
 */
void usbAudioInit(AudioSource_t *audio);

/**
 * @brief Start USB audio DMA reception
 * @return HAL_StatusTypeDef HAL_OK on success, error code otherwise
 */
HAL_StatusTypeDef usbAudioStart(void);

/**
 * @brief Stop USB audio DMA reception
 * @return HAL_StatusTypeDef HAL_OK on success, error code otherwise
 */
HAL_StatusTypeDef usbAudioStop(void);

/**
 * @brief Pause USB audio DMA reception
 * @return HAL_StatusTypeDef HAL_OK on success, error code otherwise
 */
HAL_StatusTypeDef usbAudioPause(void);

/**
 * @brief Resume USB audio DMA reception
 * @return HAL_StatusTypeDef <PERSON>AL_OK on success, error code otherwise
 */
HAL_StatusTypeDef usbAudioResume(void);

/**
 * @brief Set USB audio output volume
 * @param volume Volume level as 16-bit signed integer
 */
void usbAudioSetVolume(int16_t volume);

/**
 * @brief Mute or unmute USB audio output
 * @param mute true to mute, false to unmute
 * @return HAL_StatusTypeDef HAL_OK on success, error code otherwise
 */
HAL_StatusTypeDef usbAudioMute(bool mute);

/**
 * @brief Main USB audio processing loop
 * @param audio Pointer to AudioSource_t structure
 */
void usbAudioRun(AudioSource_t *audio);

/**
 * @brief Process audio samples through FIR filter and send via USB
 */
void usbAudioProcess(void);

/**
 * @brief Initialize FIR filter for USB audio processing
 * @param fir Pointer to FirContainer_t structure
 * @param coeffs Pointer to filter coefficients array
 * @param numTaps Number of filter taps
 * @param stateBuf Pointer to filter state buffer
 */
void USB_FirInit(FirContainer_t *fir, const float *coeffs, uint16_t numTaps, float *stateBuf);

/**
 * @brief Update FIR filter with new input samples
 * @param c Pointer to FirContainer_t structure
 * @param inp Pointer to input samples array
 */
void USB_FirUpdate(FirContainer_t *c, float *inp);


#endif /* USBAUDIO_H */