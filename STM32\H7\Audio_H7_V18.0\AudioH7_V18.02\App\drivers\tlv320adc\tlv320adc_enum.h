#ifndef __TLV320ADC_ENUM_H__
#define __TLV320ADC_ENUM_H__


enum ChannelDirection {
    INPUT,
    OUTPUT
};

enum {
    SLEEP,
    WAKE
};

enum tlvErrorCode {
    TLV_OK,
    TLV_RESET_ERROR,
    TLV_SLEEP_ERROR,
    TLV_WAKE_ERROR,
    TLV_INPUT_CHANNEL_CFG_ERROR,
    TLV_OUTPUT_CHANNEL_CFG_ERROR,
    TLV_AUDIO_CFG_ERROR,
    TLV_FILTER_CFG_ERROR,
    TLV_GPIO_CFG_ERROR,
    TLV_CHANNEL_EN_ERROR,
    TLV_POWER_UP_ERROR,

};

// Sleep Configurations 
enum AregVoltage {
    External_1_8V = 0,
   Internal_1_8V = 1
};

enum VrefCharge {
    MS3_5,
    MS10,
    MS50,
    MS100,
};

// Input Channel  Configurations
enum inputImpedance {
    IMP_2_5K = 0,
    IMP_10K = 1,
    IMP_20K = 2
};

enum inputCoupling {
    AC = 0,
    DC = 1
};

enum inputSource {
    ANALOG_DIFFERENTIAL = 0,
    ANALOG_SINGLE_ENDED = 1,
    DIGITAL_PDM = 2
};

enum inputType {
    INPUT_MICROPHONE = 0,
    INPUT_LINE = 1,
};

enum gain_sign {
    POSITIVE = 0,
    NEGATIVE = 1
};

enum gainCal {
    NEG_0_8_DB = 0,   /* Gain calibration is set to -0.8 dB */
    NEG_0_7_DB = 1,   /* Gain calibration is set to -0.7 dB */
    NEG_0_6_DB = 2,   /* Gain calibration is set to -0.6 dB */
    NEG_0_5_DB = 3,   /* Gain calibration is set to -0.5 dB (as per configuration) */
    NEG_0_4_DB = 4,   /* Gain calibration is set to -0.4 dB (as per configuration) */
    NEG_0_3_DB = 5,   /* Gain calibration is set to -0.3 dB (as per configuration) */
    NEG_0_2_DB = 6,   /* Gain calibration is set to -0.2 dB (as per configuration) */
    NEG_0_1_DB = 7,   /* Gain calibration is set to -0.1 dB (as per configuration) */
    ZERO_DB = 8,         /* Gain calibration is set to 0 dB */
    POS_0_1_DB = 9,   /* Gain calibration is set to +0.1 dB */
    POS_0_2_DB = 10,  /* Gain calibration is set to +0.2 dB (as per configuration) */
    POS_0_3_DB = 11,  /* Gain calibration is set to +0.3 dB (as per configuration) */
    POS_0_4_DB = 12,  /* Gain calibration is set to +0.4 dB (as per configuration) */
    POS_0_5_DB = 13,  /* Gain calibration is set to +0.5 dB (as per configuration) */
    POS_0_6_DB = 14,  /* Gain calibration is set to +0.6 dB */
    POS_0_7_DB = 15   /* Gain calibration is set to +0.7 dB */
};

enum Channel {
    CH1,
    CH2,
    CH3,
    CH4
};

enum ChSlot {
    L_SLOT_0 = 0,
    L_SLOT_1 = 1,
    L_SLOT_2 = 2,
    L_SLOT_3 = 3,
    R_SLOT_0 = 32,
    R_SLOT_1 = 33,
    R_SLOT_2 = 34,
    R_SLOT_3 = 35
};

// GPIO Configurations

enum GPIO_CFGs {
    GPIO_DISABLED = 0,
    GPIO_OUTPUT = 1,
    GPIO_INTERRUPT = 2,
    GPIO_PDMCLK = 4,
    GPIO_MICBIAS_ENABLE = 8,
    GPIO_INPUT = 9,
    GPIO_MCLK_INPUT = 10,
    GPIO_ASI_INPUT = 11,
    GPIO_PDM_INPUT_1 = 12,
    GPIO_PDM_INPUT_2 = 13,
};

enum GPIO_DRIVE {
    GPIO_HI_Z_OUTPUT = 0,
    GPIO_DRIVE_ACTIVE_LOW_HIGH = 1,
    GPIO_DRIVE_ACTIVE_LOW_WEAK_HIGH = 2,
    GPIO_DRIVE_ACTIVE_LOW_HIGH_Z = 3,
    GPIO_DRIVE_WEAK_LOW_ACTIVE_HIGH = 4,
    GPIO_DRIVE_HIGH_Z_ACTIVE_HIGH = 5,
};

enum GPO_CFG {
    GPO_DISABLED = 0,
    GPO_OUTPUT = 1,
    GPO_INTERRUPT = 2,
    GPO_PDMCLK = 4,
};

enum GPI_CFG {
    GPI_DISABLED = 0,
    GPI_INPUT = 1,
    GPI_MCLK_INPUT = 2,
    GPI_ASI_INPUT = 3,
    GPI_PDM_INPUT_1 = 4,
    GPI_PDM_INPUT_2 = 5,
};

// Audio Configurations

enum Mode {
    SLAVE,
    MASTER
};

enum Clock_Source {
    CLK_SRC_BCLK = 0,
    CLK_SRC_MCLK = 1,
};

enum MCLK_FREQ_SELECT_MODE {
    MCLK_FREQ_SELECT_MODE_1 = 0, // MCLK frequency is based on the MCLK_FREQ_SEL (P0_R19) configuration.
    MCLK_FREQ_SELECT_MODE_2 = 1, // MCLK frequency is specified as a multiple of FSYNC in the MCLK_RATIO_SEL (P0_R22) configuration
};

enum MCLK_RATIO_SEL {
    MCLK_RATIO_64 = 0, // MCLK is 12.288 MHz
    MCLK_RATIO_256 = 1, // MCLK is 3.072 MHz
    MCLK_RATIO_384 = 2, // MCLK is 2.048 MHz
    MCLK_RATIO_512 = 3, // MCLK is 1.536 MHz
    MCLK_RATIO_768 = 4, // MCLK is 1.024 MHz
    MCLK_RATIO_1024 = 5, // MCLK is 768 kHz
    MCLK_RATIO_1536 = 6, // MCLK is 512 kHz
    MCLK_RATIO_2304 = 7, // MCLK is 384 kHz
};

enum FS_Mode_Multiplier {
    FS_48KHZ = 0,
    FS_44_1KHZ = 1,
};

enum MCLK_FREQ {
    MCLK_12_MHZ = 0,
    MCLK_12_288_MHZ = 1,
    MCLK_13_MHZ = 2,
    MCLK_16_MHZ = 3,
    MCLK_19_2_MHZ = 4,
    MCLK_19_68_MHZ = 5,
    MCLK_24_MHZ = 6,
    MCLK_24_576_MHZ = 7,
};

// Add these enums to the existing file

// ASI Bus Sample Rate Configuration (for master mode)
enum FsRate {
    FS_RATE_7_35_8_KHZ = 0,      // 7.35 kHz or 8 kHz
    FS_RATE_14_7_16_KHZ = 1,     // 14.7 kHz or 16 kHz  
    FS_RATE_22_05_24_KHZ = 2,    // 22.05 kHz or 24 kHz
    FS_RATE_29_4_32_KHZ = 3,     // 29.4 kHz or 32 kHz
    FS_RATE_44_1_48_KHZ = 4,     // 44.1 kHz or 48 kHz (default)
    FS_RATE_88_2_96_KHZ = 5,     // 88.2 kHz or 96 kHz
    FS_RATE_176_4_192_KHZ = 6,   // 176.4 kHz or 192 kHz
    FS_RATE_352_8_384_KHZ = 7,   // 352.8 kHz or 384 kHz
    FS_RATE_705_6_768_KHZ = 8    // 705.6 kHz or 768 kHz
    // 9-15 are reserved
};

// BCLK to FSYNC Frequency Ratio Configuration (for master mode)
enum FsBclkRatio {
    FS_BCLK_RATIO_16 = 0,        // Ratio of 16
    FS_BCLK_RATIO_24 = 1,        // Ratio of 24
    FS_BCLK_RATIO_32 = 2,        // Ratio of 32
    FS_BCLK_RATIO_48 = 3,        // Ratio of 48
    FS_BCLK_RATIO_64 = 4,        // Ratio of 64
    FS_BCLK_RATIO_96 = 5,        // Ratio of 96
    FS_BCLK_RATIO_128 = 6,       // Ratio of 128
    FS_BCLK_RATIO_192 = 7,       // Ratio of 192
    FS_BCLK_RATIO_256 = 8,       // Ratio of 256 (default)
    FS_BCLK_RATIO_384 = 9,       // Ratio of 384
    FS_BCLK_RATIO_512 = 10,      // Ratio of 512
    FS_BCLK_RATIO_1024 = 11,     // Ratio of 1024
    FS_BCLK_RATIO_2048 = 12      // Ratio of 2048
    // 13-15 are reserved
};

enum ASIFormat {
    TDM,
    I2S,
    LJ
};

enum WordLength {
    BIT_16 = 0,
    BIT_20 = 1,
    BIT_24 = 2,
    BIT_32 = 3
};

enum Polarity {
    Default = 0,
    Inverted = 1
};

enum TX_Lsb {
    TRANSMIT_FULL_CYCLE = 0,
    TRANSMIT_HALF_CYCLE = 1
};

enum TxKeeper {
    BUS_KEEPER_ALWAYS_DISABLED = 0,
    BUS_KEEPER_ALWAYS_ENABLED = 1,
    BUS_KEEPER_ENABLED_DURING_LSB1 = 2,
    BUS_KEEPER_ENABLED_DURING_LSB1_5 = 3
};

enum AsiMixing {
    NO_MIXING = 0,
    OUT_CH1_2_MIX_IN_CH1 = 1,
    OUT_CH1_2_MIX_IN_CH2 = 2,
    MIX_ALL = 3
};

enum AsiMixGain {
    DB0,
    DB_NEG_6,
    DB_NEG_12,
    DB_NEG_18,
};

enum MicBiasValue {
    VREF = 0,
    VREF_X_1_096 = 1,
    VCM_IN1M = 2,
    VCM_IN2M = 3,
    VCM_IN1M_IN2M_AVG = 4,
    VCM_INTERNAL = 5,
    AVDD = 6,
    GPI2 = 7
};

enum AdcFullScale {
    V2_75,
    V2_5,
    V1_375
};

enum AgcTargetLevel {
    AGC_N6DB,
    AGC_N8DB,
    AGC_N10DB,
    AGC_N12DB,
    AGC_N14DB,
    AGC_N16DB,
    AGC_N18DB,
    AGC_N20DB,
    AGC_N22DB,
    AGC_N24DB,
    AGC_N26DB,
    AGC_N28DB,
    AGC_N30DB,
    AGC_N32DB,
    AGC_N34DB,
    AGC_N36DB,
};

enum AgcMaxGain {
    AGC_3DB,
    AGC_6DB,
    AGC_9DB,
    AGC_12DB,   
    AGC_15DB,
    AGC_18DB,
    AGC_21DB,
    AGC_24DB,
    AGC_27DB,
    AGC_30DB,
    AGC_33DB,
    AGC_36DB,
    AGC_39DB,
    AGC_42DB,
};

enum NumBiquad {
    NO_BIQUAD,
    ONE_BIQUAD,
    TWO_BIQUAD,
    THREE_BIQUAD,
};

enum DecimationFilterResponse {
    LINEAR,
    LOW_LATENCY,
    ULTRA_LOW_LATENCY
};

enum HPFSelection {
    CUSTOM,
    HPF0_00025,
    HPF0_002,
    HPF0_0208
};

enum MasterSampleRate {
    RATE_8KHZ = 0,
    RATE_16KHZ = 1,
    RATE_24KHZ = 2,
    RATE_32KHZ = 3,
    RATE_48KHZ = 4,
    RATE_96KHZ = 5,
    RATE_192KHZ = 6,
    RATE_384KHZ = 7,
    RATE_768KHZ = 8
};

enum PdmSampleRate {
    PDM_CLOCK_2_8224_MHZ,
    PDM_CLOCK_1_4112_MHZ,
    PDM_CLOCK_705_KHZ,
    PDM_CLOCK_5_6448_MHZ,
};

 enum pdm_edge {
    PDMDIN_CH1_NEG_CH2_POS = 0,         // CH1 on negative edge, CH2 on positive edge
    PDMDIN_CH1_POS_CH2_NEG = 1          // CH1 on positive edge, CH2 on negative edge
};

enum deviceMode {
    TLV_SLEEP,
    ALL_CH_PWRD_DOWN,
    ACTIVE,
};



#endif // __TLV320ADC_ENUM_H__