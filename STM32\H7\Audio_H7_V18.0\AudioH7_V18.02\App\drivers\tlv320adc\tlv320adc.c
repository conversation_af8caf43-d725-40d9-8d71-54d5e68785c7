#include "tlv320adc.h"
#include "filters.h"

int tlvReset(void)
{
    uint8_t data = 0x01;
    return tlvWrite(REG_SW_RESET, data);
}

int tlvSleep(int sleep)
{
    uint8_t data = tlv_reg[REG_SLEEP_CFG].value;
    data = (data & ~SLEEP_CFG_MASK) | ((AREG_VOLTAGE << 7) | (VREF_CHARGE << 3) | (I2C_ADDRESS_SWAP << 2) | (sleep));
    if (tlvWrite(REG_SLEEP_CFG, data) != 0)
    {
        if (sleep)
        {
            tlvDBGE("tlv Sleep failed\r\n");
        }
        else
        {
            tlvDBGE("tlv Wakeup failed\r\n");
        }
        return 1;
    }
    return 0;
}

int configureAudio(void)
{
    uint8_t data = 0;
    int ret = 0;

    data = ((ASI_FORMAT & 0x03) << 6) |
           ((WORD_LENGTH & 0x03) << 4) |
           ((FSYNC_POLARITY & 0x01) << 3) |
           ((BCLK_POLARITY & 0x01) << 2) |
           ((TX_EDGE & 0x01) << 1) |
           ((TX_FILL & 0x01));
    data &= tlv_reg[REG_ASI_CFG0].mask;
    ret += tlvWrite(REG_ASI_CFG0, data);

    data = ((TX_LSB & 0x01) << 7) |
           ((TX_KEEPER & 0x03) << 5) |
           ((TX_OFFSET & 0x1F));
    data &= tlv_reg[REG_ASI_CFG1].mask;
    ret += tlvWrite(REG_ASI_CFG1, data);

    data = ((ASI_DAISY_CHAINING_ENABLE & 0x01) << 7) |
           ((ASI_ERR_CHECK_DISABLE & 0x01) << 5) |
           ((ASI_ERR_RECOVERY_DISABLE & 0x01) << 4);
    data &= tlv_reg[REG_ASI_CFG2].mask;
    ret += tlvWrite(REG_ASI_CFG2, data);

    data = ((ASI_MIXING & 0x03) << 6) |
           ((ASI_MIX_GAIN & 0x03) << 4) |
           ((ASI_INPT_DATA_INVERSE & 0x01) << 3);
    data &= tlv_reg[REG_ASI_MIX_CFG].mask;
    ret += tlvWrite(REG_ASI_MIX_CFG, data);

    data = ((MBIAS_VALUE & 0x07) << 4) |
           (ADC_FULL_SCALE & 0x03);
    data &= tlv_reg[REG_BIAS_CFG].mask;
    ret += tlvWrite(REG_BIAS_CFG, data);

    data = ((AGC_TARGET_LEVEL & 0x0F) << 4) |
           ((AGC_MAX_GAIN & 0x0F));
    data &= tlv_reg[REG_AGC_CFG0].mask;
    ret += tlvWrite(REG_AGC_CFG0, data);

    return ret;
}

int TLV_configureClock(void)
{
    int ret = 0;
    uint8_t data = 0;

    data = ((MODE & 0x01) << 7) |
           ((AUTO_CLK_CFG_DISABLE & 0x01) << 6) |
           ((PLL_DISABLED & 0x01) << 5) |
           ((BCLK_FSYNC_GATE & 0x01) << 4) |
           ((FS_MODE & 0x01) << 3) |
           ((MCLK_FREQ & 0x07));
    data &= tlv_reg[REG_MST_CFG0].mask;
    ret += tlvWrite(REG_MST_CFG0, data);

    data = tlv_reg[REG_MST_CFG1].value;
    data = ((FS_RATE & 0x0F) << 4) | // Bits 7-4
           ((FS_BCLK_RATIO & 0x0F)); // Bits 3-0
    data &= tlv_reg[REG_MST_CFG1].mask;
    ret += tlvWrite(REG_MST_CFG1, data);

    data = tlv_reg[REG_CLK_SRC].value;
    data = ((CLOCK_SOURCE & 0x01) << 7) |
           ((MCLK_FREQ_SELECT_MODE & 0x01) << 6) |
           ((MCLK_RATIO_SEL & 0x07) << 3) |
           ((INVERT_BCLK_FOR_FSYNC & 0x01) << 1);
    data &= tlv_reg[REG_CLK_SRC].mask;
    ret += tlvWrite(REG_CLK_SRC, data);

    return ret;
}

int TLV_configurePDM(void)
{
    uint8_t data = 0;
    int ret = 0;

    // Configure PDMCLK_CFG register (0x1F)
    data = tlv_reg[REG_PDMCLK_CFG].value;  // Start with reset value 0x40
    data &= ~tlv_reg[REG_PDMCLK_CFG].mask; // Clear PDMCLK_DIV[1:0] bits (bits 1-0)
    data |= (PDM_CLOCK_DIV & 0x03);        // Set new PDM clock divider value
    ret += tlvWrite(REG_PDMCLK_CFG, data);

    // Configure PDMIN_CFG register (0x20)
    data = tlv_reg[REG_PDMIN_CFG].value;  // Start with reset value 0x00
    data &= ~tlv_reg[REG_PDMIN_CFG].mask; // Clear PDMDIN1_EDGE bit (bit 7)
    data |= ((PDM_DIN_EDGE & 0x01) << 7); // Set PDM data input edge
    ret += tlvWrite(REG_PDMIN_CFG, data);

    return ret;
}

int configureInputChannel(uint8_t *enData)
{
    int ret = 0;
    int inputChs = 0;

#ifdef IN_CH_1_EN
    inputChs++;

    InputChannelCFG InputCH1 = {
        .type = CH1_INPUT_TYPE,
        .source = CH1_INPUT_CFG,
        .coupling = CH1_INPUT_CFG,
        .impedance = CH1_IMPEDANCE,
        .AGC_enabled = CH1_AGC_ENABLE,
        .gain_db = (CH1_INPT_GAIN_DB * 2),
        .gain_sign = CH1_INPT_GAIN_SIGN,
        .DigitalVolume = CH1_INPT_VOLUME,
        .gainCal = CH1_GAIN_CALIBRATION,
        .phaseCal = CH1_PHASE_CALIBRATION,
    };

    *enData |= (1 << 7);
    ret += EnableInputChannel(CH1, &InputCH1);
#endif // IN_CH_1_EN

#ifdef IN_CH_2_EN
    inputChs++;

    InputChannelCFG InputCH2 = {
        .type = CH2_INPUT_TYPE,
        .source = CH2_INPUT_CFG,
        .coupling = CH2_INPUT_CFG,
        .impedance = CH2_IMPEDANCE,
        .AGC_enabled = CH2_AGC_ENABLE,
        .gain_db = CH2_INPT_GAIN_DB,
        .gain_sign = CH2_INPT_GAIN_SIGN,
        .DigitalVolume = CH2_INPT_VOLUME,
        .gainCal = CH2_GAIN_CALIBRATION,
        .phaseCal = CH2_PHASE_CALIBRATION,
    };

    *enData |= (1 << 6);
    ret += EnableInputChannel(CH2, &InputCH2);
#endif // IN_CH_2_EN

#ifdef IN_CH_3_EN
    inputChs++;

    InputChannelCFG InputCH3 = {
        .type = CH3_INPUT_TYPE,
        .source = CH3_INPUT_CFG,
        .coupling = CH3_INPUT_CFG,
        .impedance = CH3_IMPEDANCE,
        .AGC_enabled = CH3_AGC_ENABLE,
        .gain_db = CH3_INPT_GAIN_DB,
        .gain_sign = CH3_INPT_GAIN_SIGN,
        .DigitalVolume = CH3_INPT_VOLUME,
        .gainCal = CH3_GAIN_CALIBRATION,
        .phaseCal = CH3_PHASE_CALIBRATION,
    };

    *enData |= (1 << 5);
    ret += EnableInputChannel(CH3, &InputCH3);
#endif // IN_CH_3_EN

#ifdef IN_CH_4_EN
    inputChs++;

    InputChannelCFG InputCH4 = {
        .type = CH4_INPUT_TYPE,
        .source = CH4_INPUT_CFG,
        .coupling = CH4_INPUT_CFG,
        .impedance = CH4_IMPEDANCE,
        .AGC_enabled = CH4_AGC_ENABLE,
        .gain_db = CH4_INPT_GAIN_DB,
        .gain_sign = CH4_INPT_GAIN_SIGN,
        .DigitalVolume = CH4_INPT_VOLUME,
        .gainCal = CH4_GAIN_CALIBRATION,
        .phaseCal = CH4_PHASE_CALIBRATION,
    };

    *enData |= (1 << 4);
    ret += EnableInputChannel(CH4, &InputCH4);
#endif // IN_CH_4_EN

    setChannels(INPUT, inputChs);

    return ret;
}

int configureGPIO(void)
{
    int ret = 0;
    uint8_t data;

    /* --- GPIO1_CFG0 (addr 0x21) ---
     * bits [7:4] = GPIO1_CFG[3:0]
     * bits [2:0] = GPIO1_DRV[2:0]
     */
    data = ((GPIO_1_CONFIG & 0x0F) << 4) | (GPIO_1_DRIVE & 0x07);
    data &= tlv_reg[REG_GPIO_CFG0].mask;
    ret += tlvWrite(REG_GPIO_CFG0, data);

    /* --- GPO_CFG0 (addr 0x22) ---
     * bits [7:4] = GPO1_CFG[3:0]
     * bits [2:0] = GPO1_DRV[2:0]
     */
    data = ((GPO_1_CONFIG & 0x0F) << 4) | (GPO_1_DRIVE & 0x07);
    data &= tlv_reg[REG_GPO_CFG0].mask;
    ret += tlvWrite(REG_GPO_CFG0, data);

    /* --- GPI_CFG0 (addr 0x2B) ---
     * bits [6:4] = GPI1_CFG[2:0]
     * bits [2:0] = GPI2_CFG[2:0]
     */
    data = ((GPI_1_CONFIG & 0x07) << 4) | (GPI_2_CONFIG & 0x07);
    data &= tlv_reg[REG_GPI_CFG0].mask;
    ret += tlvWrite(REG_GPI_CFG0, data);

    return ret;
}

int EnableInputChannel(uint8_t channel, InputChannelCFG *cfg)
{
    int ret = 0;
    uint8_t data = 0;

    if (channel > 3)
    {
        tlvDBGE("TLV:Inv inpt chn\r\n");
        return -1;
    }

    uint8_t baseReg = REG_CH1_CFG0 + (channel * 5);

    data = tlv_reg[baseReg].value;
    data = ((cfg->type & 0x01) << 7) |      // 1-bit field
           ((cfg->source & 0x03) << 5) |    // 2-bit field
           ((cfg->coupling & 0x01) << 4) |  // 1-bit field
           ((cfg->impedance & 0x03) << 2) | // 2-bit field
           ((cfg->AGC_enabled & 0x01));     // 1-bit field
    data &= tlv_reg[baseReg].mask;
    ret += tlvWrite(baseReg, data);
    baseReg++;

    data = tlv_reg[baseReg].value;
    data = ((cfg->gain_db & 0x7F) << 1) |
           ((cfg->gain_sign & 0x01));
    data &= tlv_reg[baseReg].mask;
    ret += tlvWrite(baseReg, data);
    baseReg++;

    data = (cfg->DigitalVolume);
    ret += tlvWrite(baseReg, data);
    baseReg++;

    data = tlv_reg[baseReg].value;
    data = ((cfg->gainCal & 0x0F) << 4);
    data &= tlv_reg[baseReg].mask;
    ret += tlvWrite(baseReg, data);
    baseReg++;

    data = (cfg->phaseCal);
    data &= tlv_reg[baseReg].mask;
    ret += tlvWrite(baseReg, data);

    return ret;
}

int configureOutputChannel(uint8_t *enData)
{
    int ret = 0;
    uint8_t data = 0;
    *enData = 0;
    int outputChs = 0;

#ifdef OUT_CH_1_EN
    outputChs++;
    data = 0;
    data = (OUT_CH1_SLOT & 0x3F);
    *enData |= (1 << 7);
    ret += tlvWrite(REG_ASI_CH1, data);
#endif // OUT_CH_1_EN

#ifdef OUT_CH_2_EN
    outputChs++;
    data = 0;
    data = (OUT_CH2_SLOT & 0x3F);
    *enData |= (1 << 6);
    ret += tlvWrite(REG_ASI_CH2, data);
#endif // OUT_CH_2_EN

#ifdef OUT_CH_3_EN
    outputChs++;
    data = 0;
    data = (OUT_CH3_SLOT & 0x3F);
    *enData |= (1 << 5);
    ret += tlvWrite(REG_ASI_CH3, data);
#endif // OUT_CH_3_EN

#ifdef OUT_CH_4_EN
    outputChs++;
    data = 0;
    data = (OUT_CH4_SLOT & 0x3F);
    *enData |= (1 << 4);
    ret += tlvWrite(REG_ASI_CH4, data);
#endif // OUT_CH_4_EN

    setChannels(OUTPUT, outputChs);

    return ret;
}

void tlvInitFilter(void)
{
    if (NUM_FILTER_MODES > 0)
    {
        const FilterCoefficients filterModes[NUM_FILTER_MODES][MAX_NUM_BIQUADS] = {
            {EQBalanced32, EQ2Balanced32, LowPassB2Balanced32},
            {LowPassB2Lung32, EQLung32, HighPassB2Lung32},
            {EQHeart32, LowPassVHeart32, BassShelfHeart32},
            {None, None, None}};

        for (int i = 0; i < NUM_FILTER_MODES; i++)
        {
            for (int j = 0; j < MAX_NUM_BIQUADS; j++)
            {
                filterCoeffs[i][j] = filterModes[i][j];
            }
        }
    }
}

int configureFilter(int filterMode)
{
    int ret = 0;
    uint8_t data = 0;
    int outputChs = getChannels(OUTPUT);

    data = tlv_reg[REG_DSP_CFG0].value;
    data = ((OTF_VOLUME_CHANGE_DISABLE & 0x01) << 7) |
           ((DECIMATION_FILTER & 0x03) << 4) |
           ((CH_SUMMATION_EN & 0x03) << 2) |
           (HP_FILTER_SELECTION & 0x03);
    data &= tlv_reg[REG_DSP_CFG0].mask;
    ret += tlvWrite(REG_DSP_CFG0, data);

    data = tlv_reg[REG_DSP_CFG1].value;
    data = ((DVOL_CH_CTRL_DISABLE & 0x01) << 7) |
           ((NUMBER_OF_BIQUAD_FILTERS & 0x03) << 5) |
           ((SOFT_STEPPING_DISABLE & 0x01) << 4) |
           ((AGC_ENABLE & 0x01) << 3) |
           (ANTI_CLIPPER_ENABLE & 0x01);
    data &= tlv_reg[REG_DSP_CFG1].mask;
    ret += tlvWrite(REG_DSP_CFG1, data);

    for (int i = 0; i < (outputChs - 1); i++)
    {
        for (int j = 0; j < NUMBER_OF_BIQUAD_FILTERS; j++)
        {
            int flterNum = ((i * MAX_NUM_CHANNELS) + j);
            tlvSetFilter(flterNum, &filterCoeffs[filterMode][j]);
        }
    }
    return ret;
}

int tlvSetFilter(uint8_t filterNum, const FilterCoefficients *coeffs)
{
    int ret = 0;
    uint8_t page = 0;
    uint8_t baseReg = tlv_filter_base[filterNum];

    if (filterNum < 7)
    {
        page = 2;
    }
    else if (filterNum < 13)
    {
        page = 3;
    }
    else
    {
        tlvDBGE("TLV:Inv filter num\r\n");
    }

    // Set Page
    ret += tlvWriteFilter(tlv_reg[REG_PAGE_SEL].addr, page);

    // Write N0 coefficient
    ret += tlvWriteFilter(baseReg, (coeffs->N0 >> 24) & 0xFF);
    ret += tlvWriteFilter(baseReg + 1, (coeffs->N0 >> 16) & 0xFF);
    ret += tlvWriteFilter(baseReg + 2, (coeffs->N0 >> 8) & 0xFF);
    ret += tlvWriteFilter(baseReg + 3, coeffs->N0 & 0xFF);

    // Write N1 coefficient
    ret += tlvWriteFilter(baseReg + 4, (coeffs->N1 >> 24) & 0xFF);
    ret += tlvWriteFilter(baseReg + 5, (coeffs->N1 >> 16) & 0xFF);
    ret += tlvWriteFilter(baseReg + 6, (coeffs->N1 >> 8) & 0xFF);
    ret += tlvWriteFilter(baseReg + 7, (coeffs->N1 & 0xFF));

    // Write N2 coefficient
    ret += tlvWriteFilter(baseReg + 8, (coeffs->N2 >> 24) & 0xFF);
    ret += tlvWriteFilter(baseReg + 9, (coeffs->N2 >> 16) & 0xFF);
    ret += tlvWriteFilter(baseReg + 10, (coeffs->N2 >> 8) & 0xFF);
    ret += tlvWriteFilter(baseReg + 11, (coeffs->N2 & 0xFF));

    // Write D1 coefficient
    ret += tlvWriteFilter(baseReg + 12, (coeffs->D1 >> 24) & 0xFF);
    ret += tlvWriteFilter(baseReg + 13, (coeffs->D1 >> 16) & 0xFF);
    ret += tlvWriteFilter(baseReg + 14, (coeffs->D1 >> 8) & 0xFF);
    ret += tlvWriteFilter(baseReg + 15, (coeffs->D1 & 0xFF));

    // Write D2 coefficient
    ret += tlvWriteFilter(baseReg + 16, (coeffs->D2 >> 24) & 0xFF);
    ret += tlvWriteFilter(baseReg + 17, (coeffs->D2 >> 16) & 0xFF);
    ret += tlvWriteFilter(baseReg + 18, (coeffs->D2 >> 8) & 0xFF);
    ret += tlvWriteFilter(baseReg + 19, (coeffs->D2 & 0xFF));

    // Return to Page 0
    ret += tlvWriteFilter(tlv_reg[REG_PAGE_SEL].addr, 0x00);

    return ret;
}

int tlvEnableChannels(uint8_t in_ch, uint8_t out_ch)
{
    int ret = 0;

    ret += tlvWrite(REG_IN_CH_EN, in_ch);

    ret += tlvWrite(REG_ASI_OUT_CH_EN, out_ch);

    return ret;
}

int tlvPowerUp(void)
{
    int ret = 0;
    uint8_t data = 0;
    data = ((MIC_BIAS & 0x01) << 7) |
           ((ADC & 0x01) << 6) |
           ((PLL & 0x01) << 5) |
           ((DYNAMIC_CH_PWR_UP_EN & 0x01) << 4);
    data &= tlv_reg[REG_PWR_CFG].mask;
    ret = tlvWrite(REG_PWR_CFG, data);
    return ret;
}

int tlvGetStatus(tlvStatus_t *status)
{
    int err = 0;
    uint8_t data = 0;

    err = tlvRead(REG_INT_LTCH0, &data);
    if (err)
    {
        status->ErrorASIClock = 9;
        status->ErrorPllLock = 9;
    }
    else
    {
        status->ErrorASIClock = (data >> 7) & 0x01;
        status->ErrorPllLock = (data >> 6) & 0x01;
    }

    err = tlvRead(REG_DEV_STS0, &data);
    if (err)
    {
        status->CH1 = 9;
        status->CH2 = 99;
    }
    else
    {
        status->CH1 = (data >> 7) & 0x01;
        status->CH2 = (data >> 6) & 0x01;
    }

    err = tlvRead(REG_DEV_STS1, &data);
    if (err)
    {
        status->device_mode = 9;
    }
    else
    {
        uint8_t modeBits = (data >> 5) & 0x07;
        switch (modeBits)
        {
        case 4:
            status->device_mode = TLV_SLEEP;
            break;
        case 6:
            status->device_mode = ALL_CH_PWRD_DOWN;
            break;
        case 7:
            status->device_mode = ACTIVE;
            break;
        default:
            status->device_mode = 99;
            break;
        }
    }

    err = tlvRead(REG_ASI_STS, &data);
    if (err)
    {
        status->detected_fs_rate = 16;  // Error value (out of valid range)
        status->detected_fs_ratio = 16; // Error value (out of valid range)
    }
    else
    {
        // Extract FS_RATE_STS[3:0] from bits 7-4
        uint8_t fs_rate_bits = (data >> 4) & 0x0F;
        if (fs_rate_bits <= 8)
        {
            status->detected_fs_rate = fs_rate_bits; // Valid range 0-8
        }
        else if (fs_rate_bits == 15)
        {
            status->detected_fs_rate = 15; // Invalid sample rate
            tlvDBG("Detected FS rate Invalid\n");
        }
        else
        {
            status->detected_fs_rate = 14; // Reserved status (9-14)
        }

        // Extract FS_RATIO_STS[3:0] from bits 3-0
        uint8_t fs_ratio_bits = data & 0x0F;
        if (fs_ratio_bits <= 12)
        {
            status->detected_fs_ratio = fs_ratio_bits; // Valid range 0-12
        }
        else if (fs_ratio_bits == 15)
        {
            status->detected_fs_ratio = 15; // Invalid ratio
            tlvDBG("Detected FS ratio out of range\n");
        }
        else
        {
            status->detected_fs_ratio = 14; // Reserved status (13-14)
        }
    }

    return err;
}
