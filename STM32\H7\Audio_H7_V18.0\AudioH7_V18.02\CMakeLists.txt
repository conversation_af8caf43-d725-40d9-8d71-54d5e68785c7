cmake_minimum_required(VERSION 3.22)

#
# This file is generated only once,
# and is not re-generated if converter is called multiple times.
#
# User is free to modify the file as much as necessary
#

# Setup compiler settings
set(CMAKE_C_STANDARD 11)
set(CMAKE_C_STANDARD_REQUIRED ON)
set(CMAKE_C_EXTENSIONS ON)


# Define the build type
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE "Debug")
endif()

# Set the project name
set(CMAKE_PROJECT_NAME AudioH7_V18.02)

# Include toolchain file
include("cmake/gcc-arm-none-eabi.cmake")

# Enable compile command to ease indexing with e.g. clangd
set(CMAKE_EXPORT_COMPILE_COMMANDS TRUE)

# Core project settings
project(${CMAKE_PROJECT_NAME})
message("Build type: " ${CMAKE_BUILD_TYPE})

# Enable CMake support for ASM and C languages
enable_language(C ASM)

# Create an executable object type
add_executable(${CMAKE_PROJECT_NAME})

# Add STM32CubeMX generated sources
add_subdirectory(cmake/stm32cubemx)

# add USB Device Library
add_subdirectory(App/drivers/usb)

# Link directories setup
target_link_directories(${CMAKE_PROJECT_NAME} PRIVATE
    # Add user defined library search paths
)

# Gather sources from App/src, App/bq25180, and App/tlv320adc
file(GLOB APP_SOURCES ${CMAKE_SOURCE_DIR}/App/main/src/*.c)
file(GLOB ADC_SOURCES ${CMAKE_SOURCE_DIR}/App/drivers/tlv320adc/*.c)
file(GLOB AT_SOURCES ${CMAKE_SOURCE_DIR}/App/drivers/atSlave/*.c)

file(GLOB DSP_SOURCES
    ${CMAKE_SOURCE_DIR}/Drivers/CMSIS/DSP/Source/BasicMathFunctions/*.c
    ${CMAKE_SOURCE_DIR}/Drivers/CMSIS/DSP/Source/FilteringFunctions/*.c
    ${CMAKE_SOURCE_DIR}/Drivers/CMSIS/DSP/Source/SupportFunctions/*.c
    )

# Add sources to executable
target_sources(${CMAKE_PROJECT_NAME} PRIVATE
    ${APP_SOURCES}
    ${ADC_SOURCES}
    ${AT_SOURCES}
    ${DSP_SOURCES}
)

# Add include paths
target_include_directories(${CMAKE_PROJECT_NAME} PRIVATE
    ${CMAKE_SOURCE_DIR}/App/main/inc
    ${CMAKE_SOURCE_DIR}/App/drivers/atSlave
    ${CMAKE_SOURCE_DIR}/App/drivers/tlv320adc
    ${CMAKE_SOURCE_DIR}/Drivers/CMSIS/Include
    ${CMAKE_SOURCE_DIR}/Drivers/CMSIS/DSP/Include
)

# Add project symbols (macros)
target_compile_definitions(${CMAKE_PROJECT_NAME} PRIVATE
    # Add user defined symbols
    ARM_MATH_CM7
    ARM_MATH_LOOPUNROLL
)

# Add linked libraries
target_link_libraries(${CMAKE_PROJECT_NAME}
    stm32cubemx
    usb_device

    # Add user defined libraries
)
