#include "tlv320adc_reg.h"

// Register definition table stored in flash
const tlv320adc_reg_def_t tlv_reg[REG_COUNT] = {
    // System registers (page 0)
    [REG_PAGE_SEL]      = { 0x00, 0, 0x00, 0xFF },
    [REG_SW_RESET]      = { 0x01, 0, 0x00, 0x01 },
    [REG_SLEEP_CFG]     = { 0x02, 0, 0x00, 0x9F }, // bits 6:5 and bit 1 reserved
    [REG_SHDN_CFG]      = { 0x05, 0, 0x05, 0xFF },
    [REG_CLK_SRC]       = { 0x16, 0, 0x10, 0xFB }, // bit 2 reserved
    [REG_PDMCLK_CFG]    = { 0x1F, 0, 0x40, 0xFF },
    [REG_PDMIN_CFG]     = { 0x20, 0, 0x00, 0xFF },
    [REG_CM_TOL_CFG]    = { 0x3A, 0, 0x00, 0xFF },
    [REG_BIAS_CFG]      = { 0x3B, 0, 0x00, 0xFF },
    [REG_I2C_CKSUM]     = { 0x7E, 0, 0x00, 0x00 }, // read-only

    // Audio interface registers
    [REG_ASI_CFG0]      = { 0x07, 0, 0x30, 0xFF },
    [REG_ASI_CFG1]      = { 0x08, 0, 0x00, 0xFF },
    [REG_ASI_CFG2]      = { 0x09, 0, 0x00, 0xB0 }, // bits 6,5,4 valid
    [REG_ASI_MIX_CFG]   = { 0x0A, 0, 0x00, 0xF8 }, // bits 7–3 used
    [REG_ASI_CH1]       = { 0x0B, 0, 0x00, 0x3F },
    [REG_ASI_CH2]       = { 0x0C, 0, 0x01, 0x3F },
    [REG_ASI_CH3]       = { 0x0D, 0, 0x02, 0x3F },
    [REG_ASI_CH4]       = { 0x0E, 0, 0x03, 0x3F },
    [REG_MST_CFG0]      = { 0x13, 0, 0x02, 0xFF },
    [REG_MST_CFG1]      = { 0x14, 0, 0x48, 0xFF },
    [REG_ASI_STS]       = { 0x15, 0, 0xFF, 0x00 }, // read-only
    [REG_ASI_OUT_CH_EN] = { 0x74, 0, 0x00, 0xF0 },

    // GPIO registers
    [REG_GPIO_CFG0]     = { 0x21, 0, 0x22, 0xFF },
    [REG_GPO_CFG0]      = { 0x22, 0, 0x00, 0xFF },
    [REG_GPO_VAL]       = { 0x29, 0, 0x00, 0x01 },
    [REG_GPIO_MON]      = { 0x2A, 0, 0x00, 0x00 }, // read-only
    [REG_GPI_CFG0]      = { 0x2B, 0, 0x00, 0x77 }, // bits 6–4 and 2–0 used
    [REG_GPI_MON]       = { 0x2F, 0, 0x00, 0x00 }, // read-only

    // Interrupt registers
    [REG_INT_CFG]       = { 0x32, 0, 0x00, 0x01 },
    [REG_INT_MASK0]     = { 0x33, 0, 0xFF, 0xFF },
    [REG_INT_LTCH0]     = { 0x36, 0, 0x00, 0x00 }, // read-only

    // Channel configuration registers
    [REG_CH1_CFG0]      = { 0x3C, 0, 0x00, 0xFF },
    [REG_CH1_CFG1]      = { 0x3D, 0, 0x00, 0xFF },
    [REG_CH1_CFG2]      = { 0x3E, 0, 0xC9, 0xFF },
    [REG_CH1_CFG3]      = { 0x3F, 0, 0x80, 0xF0 },
    [REG_CH1_CFG4]      = { 0x40, 0, 0x00, 0xFF },
    [REG_CH2_CFG0]      = { 0x41, 0, 0x00, 0xFF },
    [REG_CH2_CFG1]      = { 0x42, 0, 0x00, 0xFF },
    [REG_CH2_CFG2]      = { 0x43, 0, 0xC9, 0xFF },
    [REG_CH2_CFG3]      = { 0x44, 0, 0x80, 0xF0 },
    [REG_CH2_CFG4]      = { 0x45, 0, 0x00, 0xFF },
    [REG_CH3_CFG2]      = { 0x48, 0, 0xC9, 0xFF },
    [REG_CH3_CFG3]      = { 0x49, 0, 0x80, 0xF0 },
    [REG_CH3_CFG4]      = { 0x4A, 0, 0x00, 0xFF },
    [REG_CH4_CFG2]      = { 0x4D, 0, 0xC9, 0xFF },
    [REG_CH4_CFG3]      = { 0x4E, 0, 0x80, 0xF0 },
    [REG_CH4_CFG4]      = { 0x4F, 0, 0x00, 0xFF },

    // DSP registers
    [REG_DSP_CFG0]      = { 0x6B, 0, 0x01, 0xFF },
    [REG_DSP_CFG1]      = { 0x6C, 0, 0x40, 0xFF },
    [REG_AGC_CFG0]      = { 0x70, 0, 0xE7, 0xFF },
    [REG_GAIN_CFG]      = { 0x71, 0, 0x00, 0xFF },

    // Power registers
    [REG_IN_CH_EN]      = { 0x73, 0, 0xC0, 0xF0 },
    [REG_PWR_CFG]       = { 0x75, 0, 0x00, 0xF0 },
    [REG_DEV_STS0]      = { 0x76, 0, 0x00, 0x00 }, // read-only
    [REG_DEV_STS1]      = { 0x77, 0, 0x80, 0x00 }, // read-only
};


const uint8_t tlv_filter_base[FLTR_REG_COUNT] = {
    0x08, // REG_BQ1_BASE (page 2)
    0x1C, // REG_BQ2_BASE (page 2)
    0x30, // REG_BQ3_BASE (page 2)
    0x44, // REG_BQ4_BASE (page 2)
    0x58, // REG_BQ5_BASE (page 2)
    0x6C, // REG_BQ6_BASE (page 2)
    0x08, // REG_BQ7_BASE (page 3)
    0x1C, // REG_BQ8_BASE (page 3)
    0x30, // REG_BQ9_BASE (page 3)
    0x44, // REG_BQ10_BASE (page 3)
    0x58, // REG_BQ11_BASE (page 3)
    0x6C, // REG_BQ12_BASE (page 3)
};

// Group index table
const tlv320adc_group_index_t tlv320adc_groups[REG_GROUP_COUNT] = {
    [REG_GROUP_SYSTEM]          = { 0, 10 },
    [REG_GROUP_AUDIO_INTERFACE] = { 10, 12 },
    [REG_GROUP_GPIO]            = { 22, 6 },
    [REG_GROUP_INTERRUPT]       = { 28, 3 },
    [REG_GROUP_CHANNEL_CONFIG]  = { 31, 16 },
    [REG_GROUP_DSP]             = { 47, 4 },
    [REG_GROUP_POWER]           = { 51, 4 }
};

tlv320adc_reg_group_t tlv320adc_get_reg_group(tlv320adc_reg_id_t reg_id) {
    for (int i = 0; i < REG_GROUP_COUNT; i++) {
        if (reg_id >= tlv320adc_groups[i].start_idx && 
            reg_id < tlv320adc_groups[i].start_idx + tlv320adc_groups[i].count) {
            return (tlv320adc_reg_group_t)i;
        }
    }
    return REG_GROUP_SYSTEM; // Default
}