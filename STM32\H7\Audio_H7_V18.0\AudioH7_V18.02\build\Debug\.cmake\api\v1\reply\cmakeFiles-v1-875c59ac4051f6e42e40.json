{"inputs": [{"path": "CMakeLists.txt"}, {"path": "cmake/gcc-arm-none-eabi.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.28.1/CMakeSystem.cmake"}, {"path": "cmake/gcc-arm-none-eabi.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.28.1/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.28.1/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Platform/Generic.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeCommonLanguageInclude.cmake"}, {"isGenerated": true, "path": "build/Debug/CMakeFiles/3.28.1/CMakeASMCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"path": "cmake/stm32cubemx/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/CMakeASMInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU-ASM.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/ST/STM32CubeCLT_1.16.0/CMake/share/cmake-3.28/Modules/Compiler/GNU.cmake"}, {"path": "App/drivers/usb/CMakeLists.txt"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02/build/Debug", "source": "C:/Users/<USER>/OneDrive/Documents/Projects/STM32/H7/Audio_H7_V18.0/AudioH7_V18.02"}, "version": {"major": 1, "minor": 0}}